{"version": 3, "sources": ["../../internal/test-utils/src/test-utils.ts"], "sourcesContent": ["import fs from \"fs\";\nimport {\n\ttype Source,\n\ttype TransformContext,\n\ttype Transformer,\n\ttype TransformerChainedResult,\n} from \"html-validate\";\n\nexport {\n\ttype Source,\n\ttype Transformer,\n\ttype TransformerResult,\n\ttype TransformerChainedResult,\n} from \"html-validate\";\n\nfunction isIterable(\n\tvalue: Source | Iterable<Source | Promise<Source>>,\n): value is Iterable<Source | Promise<Source>> {\n\treturn Symbol.iterator in value;\n}\n\n/**\n * Helper function to call a transformer function in test-cases.\n *\n * @public\n * @param fn - Transformer function to call.\n * @param filename - Filename to read data from. Must be readable.\n * @param chain - If set this function is called when chaining transformers. Default is pass-thru.\n */\nexport function transformFile(\n\tfn: Transformer,\n\tfilename: string,\n\tchain?: (source: Source, filename: string) => TransformerChainedResult,\n): Promise<Source[]> {\n\tconst data = fs.readFileSync(filename, \"utf-8\");\n\tconst source: Source = {\n\t\tfilename,\n\t\tline: 1,\n\t\tcolumn: 1,\n\t\toffset: 0,\n\t\tdata,\n\t};\n\treturn transformSource(fn, source, chain);\n}\n\n/**\n * Helper function to call a transformer function in test-cases.\n *\n * @public\n * @param fn - Transformer function to call.\n * @param data - String to transform.\n * @param chain - If set this function is called when chaining transformers. Default is pass-thru.\n */\nexport function transformString(\n\tfn: Transformer,\n\tdata: string,\n\tchain?: (source: Source, filename: string) => TransformerChainedResult,\n): Promise<Source[]> {\n\tconst source: Source = {\n\t\tfilename: \"inline\",\n\t\tline: 1,\n\t\tcolumn: 1,\n\t\toffset: 0,\n\t\tdata,\n\t};\n\treturn transformSource(fn, source, chain);\n}\n\n/**\n * Helper function to call a transformer function in test-cases.\n *\n * @public\n * @param fn - Transformer function to call.\n * @param data - Source to transform.\n * @param chain - If set this function is called when chaining transformers. Default is pass-thru.\n */\nexport async function transformSource(\n\tfn: Transformer,\n\tsource: Source,\n\tchain?: (source: Source, filename: string) => TransformerChainedResult,\n): Promise<Source[]> {\n\tconst defaultChain = (source: Source): Iterable<Source> => [source];\n\tconst context: TransformContext = {\n\t\thasChain: /* istanbul ignore next */ () => true,\n\t\tchain: chain ?? defaultChain,\n\t};\n\tconst result = await fn.call(context, source);\n\tif (isIterable(result)) {\n\t\treturn await Promise.all(Array.from(result));\n\t} else {\n\t\treturn [result];\n\t}\n}\n"], "mappings": ";AAAA,OAAO,QAAQ;AAef,SAAS,WACR,OAC8C;AAC9C,SAAO,OAAO,YAAY;AAC3B;AAUO,SAAS,cACf,IACA,UACA,OACoB;AACpB,QAAM,OAAO,GAAG,aAAa,UAAU,OAAO;AAC9C,QAAM,SAAiB;AAAA,IACtB;AAAA,IACA,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR;AAAA,EACD;AACA,SAAO,gBAAgB,IAAI,QAAQ,KAAK;AACzC;AAUO,SAAS,gBACf,IACA,MACA,OACoB;AACpB,QAAM,SAAiB;AAAA,IACtB,UAAU;AAAA,IACV,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR;AAAA,EACD;AACA,SAAO,gBAAgB,IAAI,QAAQ,KAAK;AACzC;AAUA,eAAsB,gBACrB,IACA,QACA,OACoB;AACpB,QAAM,eAAe,CAACA,YAAqC,CAACA,OAAM;AAClE,QAAM,UAA4B;AAAA,IACjC;AAAA;AAAA,MAAqC,MAAM;AAAA;AAAA,IAC3C,OAAO,SAAS;AAAA,EACjB;AACA,QAAM,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM;AAC5C,MAAI,WAAW,MAAM,GAAG;AACvB,WAAO,MAAM,QAAQ,IAAI,MAAM,KAAK,MAAM,CAAC;AAAA,EAC5C,OAAO;AACN,WAAO,CAAC,MAAM;AAAA,EACf;AACD;", "names": ["source"]}