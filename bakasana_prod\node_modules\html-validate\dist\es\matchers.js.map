{"version": 3, "file": "matchers.js", "sources": ["../../src/jest/matchers/to-be-valid.ts", "../../src/jest/matchers/to-be-invalid.ts", "../../src/jest/matchers/to-htmlvalidate.ts", "../../src/jest/matchers/to-have-error.ts", "../../src/jest/matchers/to-have-errors.ts", "../../src/jest/matchers/get-results.ts"], "sourcesContent": ["import { type Report } from \"../../reporter\";\nimport { type <PERSON><PERSON><PERSON><PERSON><PERSON>, type MaybeAsyncCallback, diverge } from \"../utils\";\n\nfunction createMatcher(): MaybeAsyncCallback<Report, []> {\n\tfunction toBeValid(report: Report): MatcherResult {\n\t\tif (report.valid) {\n\t\t\treturn {\n\t\t\t\tpass: true,\n\t\t\t\tmessage: /* istanbul ignore next */ () => \"Result should not contain error\",\n\t\t\t};\n\t\t} else {\n\t\t\tconst firstError = report.results[0].messages[0];\n\t\t\treturn {\n\t\t\t\tpass: false,\n\t\t\t\tmessage: () => `Result should be valid but had error \"${firstError.message}\"`,\n\t\t\t};\n\t\t}\n\t}\n\treturn diverge(toBeValid);\n}\n\nexport { createMatcher as toBeValid };\n", "import { type Report } from \"../../reporter\";\nimport { type <PERSON><PERSON><PERSON><PERSON><PERSON>, type MaybeAsyncCallback, diverge } from \"../utils\";\n\nfunction createMatcher(): MaybeAsyncCallback<Report, []> {\n\tfunction toBeInvalid(report: Report): MatcherResult {\n\t\tif (report.valid) {\n\t\t\treturn {\n\t\t\t\tpass: false,\n\t\t\t\tmessage: () => \"Result should be invalid but had no errors\",\n\t\t\t};\n\t\t} else {\n\t\t\treturn {\n\t\t\t\tpass: true,\n\t\t\t\tmessage: /* istanbul ignore next */ () => \"Result should not contain error\",\n\t\t\t};\n\t\t}\n\t}\n\treturn diverge(toBeInvalid);\n}\n\nexport { createMatcher as toBeInvalid };\n", "import deepmerge from \"deepmerge\";\nimport { type ConfigData } from \"../../config\";\nimport { type Message } from \"../../message\";\nimport {\n\ttype DiffFunction,\n\ttype MatcherContext,\n\ttype MatcherExpect,\n\ttype MatcherResult,\n\ttype MaybeAsyncCallback,\n\tdiverge,\n} from \"../utils\";\nimport { type ValidateStringFn, createSyncFn, workerPath } from \"../worker\";\n\nfunction isMessage(arg: any): arg is Partial<Message> {\n\tif (!arg) {\n\t\treturn false;\n\t}\n\treturn Boolean(\n\t\targ.ruleId ??\n\t\t\targ.severity ??\n\t\t\targ.message ??\n\t\t\targ.offset ??\n\t\t\targ.line ??\n\t\t\targ.column ??\n\t\t\targ.size ??\n\t\t\targ.selector ??\n\t\t\targ.context,\n\t);\n}\n\nfunction isConfig(arg: any): arg is ConfigData {\n\tif (!arg) {\n\t\treturn false;\n\t}\n\treturn Boolean(\n\t\targ.root ?? arg.extends ?? arg.elements ?? arg.plugin ?? arg.transform ?? arg.rules,\n\t);\n}\n\nfunction isString(arg: any): arg is string {\n\treturn typeof arg === \"string\";\n}\n\nfunction getMarkup(src: unknown): string {\n\tif (typeof HTMLElement !== \"undefined\" && src instanceof HTMLElement) {\n\t\treturn (src as { outerHTML: string }).outerHTML;\n\t}\n\t/* istanbul ignore else: prototype only allows string or HTMLElement */\n\tif (typeof src === \"string\") {\n\t\treturn src;\n\t} else {\n\t\tthrow new Error(`Failed to get markup from \"${typeof src}\" argument`);\n\t}\n}\n\ntype Arg1 = Partial<Message> | ConfigData | string;\ntype Arg2 = ConfigData | string;\ntype Arg3 = string; /* eslint-disable-line sonarjs/redundant-type-aliases -- for consistency */\n\nfunction createMatcher(\n\texpect: MatcherExpect,\n\tdiff: DiffFunction | undefined,\n): MaybeAsyncCallback<unknown, [Arg1?, Arg2?, Arg3?]> {\n\tfunction toHTMLValidate(\n\t\tthis: MatcherContext,\n\t\tactual: unknown,\n\t\targ0?: Arg1,\n\t\targ1?: Arg2,\n\t\targ2?: Arg3,\n\t): MatcherResult {\n\t\tconst markup = getMarkup(actual);\n\t\tconst message = isMessage(arg0) ? arg0 : undefined;\n\t\tconst config = isConfig(arg0) ? arg0 : isConfig(arg1) ? arg1 : undefined; // eslint-disable-line sonarjs/no-nested-conditional -- easier to read than the alternative */\n\t\tconst filename = isString(arg0) ? arg0 : isString(arg1) ? arg1 : arg2; // eslint-disable-line sonarjs/no-nested-conditional -- easier to read than the alternative */\n\t\treturn toHTMLValidateImpl.call(this, expect, diff, markup, message, config, filename);\n\t}\n\treturn diverge(toHTMLValidate);\n}\n\nfunction toHTMLValidateImpl(\n\tthis: MatcherContext,\n\texpect: MatcherExpect,\n\tdiff: DiffFunction | undefined,\n\tactual: string,\n\texpectedError?: Partial<Message>,\n\tuserConfig?: ConfigData,\n\tfilename?: string,\n): MatcherResult {\n\tconst defaultConfig = {\n\t\trules: {\n\t\t\t/* jsdom normalizes style so disabling rule when using this matcher or it\n\t\t\t * gets quite noisy when configured with self-closing */\n\t\t\t\"void-style\": \"off\",\n\t\t},\n\t};\n\tconst config = deepmerge(defaultConfig, userConfig ?? {});\n\t/* istanbul ignore next: cant figure out when this would be unset */\n\tconst actualFilename = filename ?? this.testPath ?? \"inline\";\n\n\tconst syncFn = createSyncFn<ValidateStringFn>(workerPath);\n\tconst report = syncFn(actual, actualFilename, config);\n\tconst pass = report.valid;\n\tconst result = report.results[0];\n\tif (pass) {\n\t\treturn { pass, message: () => \"HTML is valid when an error was expected\" };\n\t} else {\n\t\tif (expectedError) {\n\t\t\tconst actual = result.messages;\n\t\t\tconst expected = expect.arrayContaining([expect.objectContaining(expectedError)]);\n\t\t\tconst errorPass = this.equals(actual, expected);\n\t\t\tconst diffString = diff\n\t\t\t\t? diff(expected, actual, {\n\t\t\t\t\t\texpand: this.expand,\n\t\t\t\t\t\taAnnotation: \"Expected error\",\n\t\t\t\t\t\tbAnnotation: \"Actual error\",\n\t\t\t\t\t})\n\t\t\t\t: /* istanbul ignore next */ undefined;\n\t\t\tconst hint = this.utils.matcherHint(\".not.toHTMLValidate\", undefined, undefined, {\n\t\t\t\tcomment: \"expected error\",\n\t\t\t});\n\t\t\tconst expectedErrorMessage = (): string =>\n\t\t\t\t[\n\t\t\t\t\thint,\n\t\t\t\t\t\"\",\n\t\t\t\t\t\"Expected error to be present:\",\n\t\t\t\t\tthis.utils.printExpected(expectedError),\n\t\t\t\t\t/* istanbul ignore next */ diffString ? `\\n${diffString}` : \"\",\n\t\t\t\t].join(\"\\n\");\n\t\t\treturn { pass: !errorPass, message: expectedErrorMessage, actual, expected };\n\t\t}\n\n\t\tconst errors = result.messages.map((message) => `  ${message.message} [${message.ruleId}]`);\n\t\treturn {\n\t\t\tpass,\n\t\t\tmessage: () =>\n\t\t\t\t[\"Expected HTML to be valid but had the following errors:\", \"\"].concat(errors).join(\"\\n\"),\n\t\t};\n\t}\n}\n\nexport { createMatcher as toHTMLValidate };\n", "import { type Message } from \"../../message\";\nimport { type Report } from \"../../reporter\";\nimport {\n\ttype DiffFunction,\n\ttype MatcherContext,\n\ttype MatcherExpect,\n\ttype Matcher<PERSON>esult,\n\ttype MaybeAsyncCallback,\n\tdiverge,\n} from \"../utils\";\nimport { flattenMessages } from \"../utils/flatten-messages\";\n\nfunction toHaveErrorImpl(\n\tcontext: MatcherContext,\n\texpect: MatcherExpect,\n\tdiff: DiffFunction | undefined,\n\tactual: Report,\n\texpected: Partial<Message>,\n): MatcherResult {\n\tconst flattened = flattenMessages(actual);\n\tconst matcher = [expect.objectContaining(expected)];\n\tconst pass = context.equals(flattened, matcher);\n\tconst diffString = diff\n\t\t? diff(matcher, flattened, { expand: context.expand })\n\t\t: /* istanbul ignore next */ undefined;\n\tconst hint = context.utils.matcherHint(\".toHaveError\");\n\tconst prettyExpected = context.utils.printExpected(matcher);\n\tconst prettyReceived = context.utils.printReceived(flattened);\n\tconst resultMessage = (): string => {\n\t\treturn [\n\t\t\thint,\n\t\t\t\"\",\n\t\t\t\"Expected error to equal:\",\n\t\t\t`  ${prettyExpected}`,\n\t\t\t\"Received:\",\n\t\t\t`  ${prettyReceived}`,\n\t\t\t/* istanbul ignore next */ diffString ? `\\nDifference:\\n\\n${diffString}` : \"\",\n\t\t].join(\"\\n\");\n\t};\n\treturn { pass, message: resultMessage, actual: flattened, expected: matcher };\n}\n\nfunction createMatcher(\n\texpect: MatcherExpect,\n\tdiff: DiffFunction | undefined,\n):\n\t| MaybeAsyncCallback<Report, [Partial<Message>]>\n\t| MaybeAsyncCallback<Report, [string, string, any?]> {\n\tfunction toHaveError(\n\t\tthis: MatcherContext,\n\t\tactual: Report,\n\t\terror: Partial<Message>,\n\t): MatcherResult;\n\tfunction toHaveError(\n\t\tthis: MatcherContext,\n\t\tactual: Report,\n\t\truleId: string,\n\t\tmessage: string,\n\t\tcontext?: any,\n\t): MatcherResult;\n\tfunction toHaveError(\n\t\tthis: MatcherContext,\n\t\tactual: Report,\n\t\targ1: string | Partial<Message>,\n\t\targ2?: string,\n\t\targ3?: any,\n\t): MatcherResult {\n\t\tif (typeof arg1 === \"string\") {\n\t\t\tconst expected: Partial<Message> = {\n\t\t\t\truleId: arg1,\n\t\t\t\tmessage: arg2,\n\t\t\t};\n\t\t\tif (arg3) {\n\t\t\t\t/* eslint-disable-next-line @typescript-eslint/no-unsafe-assignment -- this is supposed to accept anything */\n\t\t\t\texpected.context = arg3;\n\t\t\t}\n\t\t\treturn toHaveErrorImpl(this, expect, diff, actual, expected);\n\t\t} else {\n\t\t\treturn toHaveErrorImpl(this, expect, diff, actual, arg1);\n\t\t}\n\t}\n\treturn diverge(toHaveError);\n}\n\nexport { createMatcher as toHaveError };\n", "/* eslint-disable prefer-template -- technical debt, should be refactored */\n\nimport { type Report } from \"../../reporter\";\nimport {\n\ttype DiffFunction,\n\ttype Matcher<PERSON>ontext,\n\ttype MatcherExpect,\n\ttype MatcherResult,\n\ttype MaybeAsyncCallback,\n\tdiverge,\n\tflattenMessages,\n} from \"../utils\";\n\nfunction createMatcher(\n\texpect: MatcherExpect,\n\tdiff: DiffFunction | undefined,\n): MaybeAsyncCallback<Report, [Array<[string, string] | Record<string, unknown>>]> {\n\tfunction toHaveErrors(\n\t\tthis: MatcherContext,\n\t\treport: Report,\n\t\terrors: Array<[string, string] | Record<string, unknown>>,\n\t): MatcherResult {\n\t\tconst flattened = flattenMessages(report);\n\t\tconst matcher = errors.map((entry) => {\n\t\t\tif (Array.isArray(entry)) {\n\t\t\t\tconst [ruleId, message] = entry;\n\t\t\t\treturn expect.objectContaining({ ruleId, message });\n\t\t\t} else {\n\t\t\t\treturn expect.objectContaining(entry);\n\t\t\t}\n\t\t});\n\t\tconst pass = this.equals(flattened, matcher);\n\t\tconst diffString = diff\n\t\t\t? diff(matcher, flattened, { expand: this.expand })\n\t\t\t: /* istanbul ignore next */ undefined;\n\t\tconst resultMessage = (): string =>\n\t\t\tthis.utils.matcherHint(\".toHaveErrors\") +\n\t\t\t\"\\n\\n\" +\n\t\t\t\"Expected error to equal:\\n\" +\n\t\t\t`  ${this.utils.printExpected(matcher)}\\n` +\n\t\t\t\"Received:\\n\" +\n\t\t\t`  ${this.utils.printReceived(flattened)}` +\n\t\t\t/* istanbul ignore next */ (diffString ? `\\n\\nDifference:\\n\\n${diffString}` : \"\");\n\n\t\treturn { pass, message: resultMessage };\n\t}\n\treturn diverge(toHaveErrors);\n}\n\nexport { createMatcher as toHaveErrors };\n", "import { type Report, type Result } from \"../../reporter\";\nimport { type ValidateStringFn, createSyncFn, workerPath } from \"../worker\";\n\n/**\n * @internal\n */\nexport function getResults(filename: string, value: Report | string): Result[] {\n\tif (typeof value === \"string\") {\n\t\tconst syncFn = createSyncFn<ValidateStringFn>(workerPath);\n\t\tconst report = syncFn(value, filename, {\n\t\t\trules: {\n\t\t\t\t\"void-style\": \"off\",\n\t\t\t},\n\t\t});\n\t\treturn report.results.map((it) => {\n\t\t\treturn { ...it, filePath: \"inline\" };\n\t\t});\n\t} else {\n\t\treturn value.results;\n\t}\n}\n"], "names": ["createMatcher", "actual"], "mappings": ";;;AAGA,SAASA,eAAA,GAAgD;AACxD,EAAA,SAAS,UAAU,MAAA,EAA+B;AACjD,IAAA,IAAI,OAAO,KAAA,EAAO;AACjB,MAAA,OAAO;AAAA,QACN,IAAA,EAAM,IAAA;AAAA,QACN,OAAA;AAAA;AAAA,UAAoC,MAAM;AAAA;AAAA,OAC3C;AAAA,KACD,MAAO;AACN,MAAA,MAAM,aAAa,MAAA,CAAO,OAAA,CAAQ,CAAC,CAAA,CAAE,SAAS,CAAC,CAAA;AAC/C,MAAA,OAAO;AAAA,QACN,IAAA,EAAM,KAAA;AAAA,QACN,OAAA,EAAS,MAAM,CAAA,sCAAA,EAAyC,UAAA,CAAW,OAAO,CAAA,CAAA;AAAA,OAC3E;AAAA;AACD;AAED,EAAA,OAAO,QAAQ,SAAS,CAAA;AACzB;;AChBA,SAASA,eAAA,GAAgD;AACxD,EAAA,SAAS,YAAY,MAAA,EAA+B;AACnD,IAAA,IAAI,OAAO,KAAA,EAAO;AACjB,MAAA,OAAO;AAAA,QACN,IAAA,EAAM,KAAA;AAAA,QACN,SAAS,MAAM;AAAA,OAChB;AAAA,KACD,MAAO;AACN,MAAA,OAAO;AAAA,QACN,IAAA,EAAM,IAAA;AAAA,QACN,OAAA;AAAA;AAAA,UAAoC,MAAM;AAAA;AAAA,OAC3C;AAAA;AACD;AAED,EAAA,OAAO,QAAQ,WAAW,CAAA;AAC3B;;ACLA,SAAS,UAAU,GAAA,EAAmC;AACrD,EAAA,IAAI,CAAC,GAAA,EAAK;AACT,IAAA,OAAO,KAAA;AAAA;AAER,EAAA,OAAO,OAAA;AAAA,IACN,IAAI,MAAA,IACH,GAAA,CAAI,QAAA,IACJ,GAAA,CAAI,WACJ,GAAA,CAAI,MAAA,IACJ,GAAA,CAAI,IAAA,IACJ,IAAI,MAAA,IACJ,GAAA,CAAI,IAAA,IACJ,GAAA,CAAI,YACJ,GAAA,CAAI;AAAA,GACN;AACD;AAEA,SAAS,SAAS,GAAA,EAA6B;AAC9C,EAAA,IAAI,CAAC,GAAA,EAAK;AACT,IAAA,OAAO,KAAA;AAAA;AAER,EAAA,OAAO,OAAA;AAAA,IACN,GAAA,CAAI,IAAA,IAAQ,GAAA,CAAI,OAAA,IAAW,GAAA,CAAI,YAAY,GAAA,CAAI,MAAA,IAAU,GAAA,CAAI,SAAA,IAAa,GAAA,CAAI;AAAA,GAC/E;AACD;AAEA,SAAS,SAAS,GAAA,EAAyB;AAC1C,EAAA,OAAO,OAAO,GAAA,KAAQ,QAAA;AACvB;AAEA,SAAS,UAAU,GAAA,EAAsB;AACxC,EAAA,IAAI,OAAO,WAAA,KAAgB,WAAA,IAAe,GAAA,YAAe,WAAA,EAAa;AACrE,IAAA,OAAQ,GAAA,CAA8B,SAAA;AAAA;AAGvC,EAAA,IAAI,OAAO,QAAQ,QAAA,EAAU;AAC5B,IAAA,OAAO,GAAA;AAAA,GACR,MAAO;AACN,IAAA,MAAM,IAAI,KAAA,CAAM,CAAA,2BAAA,EAA8B,OAAO,GAAG,CAAA,UAAA,CAAY,CAAA;AAAA;AAEtE;AAMA,SAASA,eAAA,CACR,QACA,IAAA,EACqD;AACrD,EAAA,SAAS,cAAA,CAER,MAAA,EACA,IAAA,EACA,IAAA,EACA,IAAA,EACgB;AAChB,IAAA,MAAM,MAAA,GAAS,UAAU,MAAM,CAAA;AAC/B,IAAA,MAAM,OAAA,GAAU,SAAA,CAAU,IAAI,CAAA,GAAI,IAAA,GAAO,MAAA;AACzC,IAAA,MAAM,MAAA,GAAS,SAAS,IAAI,CAAA,GAAI,OAAO,QAAA,CAAS,IAAI,IAAI,IAAA,GAAO,MAAA;AAC/D,IAAA,MAAM,QAAA,GAAW,SAAS,IAAI,CAAA,GAAI,OAAO,QAAA,CAAS,IAAI,IAAI,IAAA,GAAO,IAAA;AACjE,IAAA,OAAO,kBAAA,CAAmB,KAAK,IAAA,EAAM,MAAA,EAAQ,MAAM,MAAA,EAAQ,OAAA,EAAS,QAAQ,QAAQ,CAAA;AAAA;AAErF,EAAA,OAAO,QAAQ,cAAc,CAAA;AAC9B;AAEA,SAAS,mBAER,MAAA,EACA,IAAA,EACA,MAAA,EACA,aAAA,EACA,YACA,QAAA,EACgB;AAChB,EAAA,MAAM,aAAA,GAAgB;AAAA,IACrB,KAAA,EAAO;AAAA;AAAA;AAAA,MAGN,YAAA,EAAc;AAAA;AACf,GACD;AACA,EAAA,MAAM,MAAA,GAAS,SAAA,CAAU,aAAA,EAAe,UAAA,IAAc,EAAE,CAAA;AAExD,EAAA,MAAM,cAAA,GAAiB,QAAA,IAAY,IAAA,CAAK,QAAA,IAAY,QAAA;AAEpD,EAAA,MAAM,MAAA,GAAS,aAA+B,UAAU,CAAA;AACxD,EAAA,MAAM,MAAA,GAAS,MAAA,CAAO,MAAA,EAAQ,cAAA,EAAgB,MAAM,CAAA;AACpD,EAAA,MAAM,OAAO,MAAA,CAAO,KAAA;AACpB,EAAA,MAAM,MAAA,GAAS,MAAA,CAAO,OAAA,CAAQ,CAAC,CAAA;AAC/B,EAAA,IAAI,IAAA,EAAM;AACT,IAAA,OAAO,EAAE,IAAA,EAAM,OAAA,EAAS,MAAM,0CAAA,EAA2C;AAAA,GAC1E,MAAO;AACN,IAAA,IAAI,aAAA,EAAe;AAClB,MAAA,MAAMC,UAAS,MAAA,CAAO,QAAA;AACtB,MAAA,MAAM,QAAA,GAAW,OAAO,eAAA,CAAgB,CAAC,OAAO,gBAAA,CAAiB,aAAa,CAAC,CAAC,CAAA;AAChF,MAAA,MAAM,SAAA,GAAY,IAAA,CAAK,MAAA,CAAOA,OAAAA,EAAQ,QAAQ,CAAA;AAC9C,MAAA,MAAM,UAAA,GAAa,IAAA,GAChB,IAAA,CAAK,QAAA,EAAUA,OAAAA,EAAQ;AAAA,QACvB,QAAQ,IAAA,CAAK,MAAA;AAAA,QACb,WAAA,EAAa,gBAAA;AAAA,QACb,WAAA,EAAa;AAAA,OACb,CAAA;AAAA;AAAA,QAC2B;AAAA,OAAA;AAC9B,MAAA,MAAM,OAAO,IAAA,CAAK,KAAA,CAAM,WAAA,CAAY,qBAAA,EAAuB,QAAW,MAAA,EAAW;AAAA,QAChF,OAAA,EAAS;AAAA,OACT,CAAA;AACD,MAAA,MAAM,uBAAuB,MAC5B;AAAA,QACC,IAAA;AAAA,QACA,EAAA;AAAA,QACA,+BAAA;AAAA,QACA,IAAA,CAAK,KAAA,CAAM,aAAA,CAAc,aAAa,CAAA;AAAA;AAAA,QACX,UAAA,GAAa;AAAA,EAAK,UAAU,CAAA,CAAA,GAAK;AAAA,OAC7D,CAAE,KAAK,IAAI,CAAA;AACZ,MAAA,OAAO,EAAE,MAAM,CAAC,SAAA,EAAW,SAAS,oBAAA,EAAsB,MAAA,EAAAA,SAAQ,QAAA,EAAS;AAAA;AAG5E,IAAA,MAAM,MAAA,GAAS,MAAA,CAAO,QAAA,CAAS,GAAA,CAAI,CAAC,OAAA,KAAY,CAAA,EAAA,EAAK,OAAA,CAAQ,OAAO,CAAA,EAAA,EAAK,OAAA,CAAQ,MAAM,CAAA,CAAA,CAAG,CAAA;AAC1F,IAAA,OAAO;AAAA,MACN,IAAA;AAAA,MACA,OAAA,EAAS,MACR,CAAC,yDAAA,EAA2D,EAAE,EAAE,MAAA,CAAO,MAAM,CAAA,CAAE,IAAA,CAAK,IAAI;AAAA,KAC1F;AAAA;AAEF;;AC9HA,SAAS,eAAA,CACR,OAAA,EACA,MAAA,EACA,IAAA,EACA,QACA,QAAA,EACgB;AAChB,EAAA,MAAM,SAAA,GAAY,gBAAgB,MAAM,CAAA;AACxC,EAAA,MAAM,OAAA,GAAU,CAAC,MAAA,CAAO,gBAAA,CAAiB,QAAQ,CAAC,CAAA;AAClD,EAAA,MAAM,IAAA,GAAO,OAAA,CAAQ,MAAA,CAAO,SAAA,EAAW,OAAO,CAAA;AAC9C,EAAA,MAAM,UAAA,GAAa,OAChB,IAAA,CAAK,OAAA,EAAS,WAAW,EAAE,MAAA,EAAQ,OAAA,CAAQ,MAAA,EAAQ,CAAA;AAAA;AAAA,IACxB;AAAA,GAAA;AAC9B,EAAA,MAAM,IAAA,GAAO,OAAA,CAAQ,KAAA,CAAM,WAAA,CAAY,cAAc,CAAA;AACrD,EAAA,MAAM,cAAA,GAAiB,OAAA,CAAQ,KAAA,CAAM,aAAA,CAAc,OAAO,CAAA;AAC1D,EAAA,MAAM,cAAA,GAAiB,OAAA,CAAQ,KAAA,CAAM,aAAA,CAAc,SAAS,CAAA;AAC5D,EAAA,MAAM,gBAAgB,MAAc;AACnC,IAAA,OAAO;AAAA,MACN,IAAA;AAAA,MACA,EAAA;AAAA,MACA,0BAAA;AAAA,MACA,KAAK,cAAc,CAAA,CAAA;AAAA,MACnB,WAAA;AAAA,MACA,KAAK,cAAc,CAAA,CAAA;AAAA;AAAA,MACQ,UAAA,GAAa;AAAA;;AAAA,EAAoB,UAAU,CAAA,CAAA,GAAK;AAAA,KAC5E,CAAE,KAAK,IAAI,CAAA;AAAA,GACZ;AACA,EAAA,OAAO,EAAE,IAAA,EAAM,OAAA,EAAS,eAAe,MAAA,EAAQ,SAAA,EAAW,UAAU,OAAA,EAAQ;AAC7E;AAEA,SAASD,eAAA,CACR,QACA,IAAA,EAGqD;AAarD,EAAA,SAAS,WAAA,CAER,MAAA,EACA,IAAA,EACA,IAAA,EACA,IAAA,EACgB;AAChB,IAAA,IAAI,OAAO,SAAS,QAAA,EAAU;AAC7B,MAAA,MAAM,QAAA,GAA6B;AAAA,QAClC,MAAA,EAAQ,IAAA;AAAA,QACR,OAAA,EAAS;AAAA,OACV;AACA,MAAA,IAAI,IAAA,EAAM;AAET,QAAA,QAAA,CAAS,OAAA,GAAU,IAAA;AAAA;AAEpB,MAAA,OAAO,eAAA,CAAgB,IAAA,EAAM,MAAA,EAAQ,IAAA,EAAM,QAAQ,QAAQ,CAAA;AAAA,KAC5D,MAAO;AACN,MAAA,OAAO,eAAA,CAAgB,IAAA,EAAM,MAAA,EAAQ,IAAA,EAAM,QAAQ,IAAI,CAAA;AAAA;AACxD;AAED,EAAA,OAAO,QAAQ,WAAW,CAAA;AAC3B;;ACrEA,SAAS,aAAA,CACR,QACA,IAAA,EACkF;AAClF,EAAA,SAAS,YAAA,CAER,QACA,MAAA,EACgB;AAChB,IAAA,MAAM,SAAA,GAAY,gBAAgB,MAAM,CAAA;AACxC,IAAA,MAAM,OAAA,GAAU,MAAA,CAAO,GAAA,CAAI,CAAC,KAAA,KAAU;AACrC,MAAA,IAAI,KAAA,CAAM,OAAA,CAAQ,KAAK,CAAA,EAAG;AACzB,QAAA,MAAM,CAAC,MAAA,EAAQ,OAAO,CAAA,GAAI,KAAA;AAC1B,QAAA,OAAO,MAAA,CAAO,gBAAA,CAAiB,EAAE,MAAA,EAAQ,SAAS,CAAA;AAAA,OACnD,MAAO;AACN,QAAA,OAAO,MAAA,CAAO,iBAAiB,KAAK,CAAA;AAAA;AACrC,KACA,CAAA;AACD,IAAA,MAAM,IAAA,GAAO,IAAA,CAAK,MAAA,CAAO,SAAA,EAAW,OAAO,CAAA;AAC3C,IAAA,MAAM,UAAA,GAAa,OAChB,IAAA,CAAK,OAAA,EAAS,WAAW,EAAE,MAAA,EAAQ,IAAA,CAAK,MAAA,EAAQ,CAAA;AAAA;AAAA,MACrB;AAAA,KAAA;AAC9B,IAAA,MAAM,gBAAgB,MACrB,IAAA,CAAK,KAAA,CAAM,WAAA,CAAY,eAAe,CAAA,GACtC;;AAAA;AAAA,EAAA,EAEK,IAAA,CAAK,KAAA,CAAM,aAAA,CAAc,OAAO,CAAC;AAAA;AAAA,EAAA,EAEjC,IAAA,CAAK,KAAA,CAAM,aAAA,CAAc,SAAS,CAAC,CAAA,CAAA;AAAA,KACZ,UAAA,GAAa;;AAAA;;AAAA,EAAsB,UAAU,CAAA,CAAA,GAAK,EAAA,CAAA;AAE/E,IAAA,OAAO,EAAE,IAAA,EAAM,OAAA,EAAS,aAAA,EAAc;AAAA;AAEvC,EAAA,OAAO,QAAQ,YAAY,CAAA;AAC5B;;ACzCO,SAAS,UAAA,CAAW,UAAkB,KAAA,EAAkC;AAC9E,EAAA,IAAI,OAAO,UAAU,QAAA,EAAU;AAC9B,IAAA,MAAM,MAAA,GAAS,aAA+B,UAAU,CAAA;AACxD,IAAA,MAAM,MAAA,GAAS,MAAA,CAAO,KAAA,EAAO,QAAA,EAAU;AAAA,MACtC,KAAA,EAAO;AAAA,QACN,YAAA,EAAc;AAAA;AACf,KACA,CAAA;AACD,IAAA,OAAO,MAAA,CAAO,OAAA,CAAQ,GAAA,CAAI,CAAC,EAAA,KAAO;AACjC,MAAA,OAAO,EAAE,GAAG,EAAA,EAAI,QAAA,EAAU,QAAA,EAAS;AAAA,KACnC,CAAA;AAAA,GACF,MAAO;AACN,IAAA,OAAO,KAAA,CAAM,OAAA;AAAA;AAEf;;;;"}