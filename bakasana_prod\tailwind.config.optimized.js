
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      // Only include colors actually used
      colors: {
        'sanctuary': '#FDFCF8',
        'linen': '#F5F1E8',
        'temple-gold': '#C9A575',
        'charcoal': '#2C2C2C',
      },
      // Only include fonts actually used
      fontFamily: {
        'primary': ['Cormorant Garamond', 'serif'],
        'secondary': ['Inter', 'sans-serif'],
      },
      // Optimize animations
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
  // Optimize for production
  corePlugins: {
    // Disable unused core plugins
    preflight: true,
    container: false, // Use custom container
    accessibility: true,
    pointerEvents: true,
    visibility: true,
    position: true,
    inset: true,
    isolation: false, // Rarely used
    zIndex: true,
    order: false, // Use flexbox order sparingly
    gridColumn: true,
    gridColumnStart: false,
    gridColumnEnd: false,
    gridRow: true,
    gridRowStart: false,
    gridRowEnd: false,
    float: false, // Modern layouts don't need float
    clear: false,
    boxSizing: true,
    display: true,
    aspectRatio: true,
    size: true,
    height: true,
    maxHeight: true,
    minHeight: true,
    width: true,
    minWidth: true,
    maxWidth: true,
    flex: true,
    flexShrink: true,
    flexGrow: true,
    flexBasis: true,
    tableLayout: false, // Rarely used
    captionSide: false,
    borderCollapse: false,
    borderSpacing: false,
    transformOrigin: true,
    translate: true,
    rotate: true,
    skew: false, // Rarely used
    scale: true,
    transform: true,
    animation: true,
    cursor: true,
    touchAction: true,
    userSelect: true,
    resize: false, // Rarely used
    scrollSnapType: false,
    scrollSnapAlign: false,
    scrollSnapStop: false,
    scrollMargin: false,
    scrollPadding: false,
    listStylePosition: false,
    listStyleType: false,
    appearance: true,
    columns: false, // CSS columns rarely used
    breakBefore: false,
    breakInside: false,
    breakAfter: false,
    gridAutoColumns: false,
    gridAutoFlow: false,
    gridAutoRows: false,
    gridTemplateColumns: true,
    gridTemplateRows: true,
    flexDirection: true,
    flexWrap: true,
    placeContent: true,
    placeItems: true,
    alignContent: true,
    alignItems: true,
    justifyContent: true,
    justifyItems: true,
    gap: true,
    space: true,
    divideWidth: false, // Use borders instead
    divideColor: false,
    divideStyle: false,
    divideOpacity: false,
    placeSelf: false,
    alignSelf: true,
    justifySelf: false,
    overflow: true,
    overscrollBehavior: false,
    scrollBehavior: true,
    textOverflow: true,
    hyphens: false,
    whitespace: true,
    wordBreak: false,
    borderRadius: true,
    borderWidth: true,
    borderColor: true,
    borderStyle: true,
    borderOpacity: true,
    backgroundColor: true,
    backgroundOpacity: true,
    backgroundImage: true,
    gradientColorStops: true,
    backgroundSize: true,
    backgroundAttachment: false,
    backgroundClip: true,
    backgroundPosition: true,
    backgroundRepeat: true,
    backgroundOrigin: false,
    fill: true,
    stroke: true,
    strokeWidth: true,
    objectFit: true,
    objectPosition: true,
    padding: true,
    margin: true,
    fontFamily: true,
    fontSize: true,
    fontWeight: true,
    fontVariantNumeric: false,
    letterSpacing: true,
    lineHeight: true,
    listStyleImage: false,
    textAlign: true,
    textColor: true,
    textOpacity: true,
    textDecoration: true,
    textDecorationColor: false,
    textDecorationStyle: false,
    textDecorationThickness: false,
    textUnderlineOffset: false,
    fontStyle: true,
    fontVariant: false,
    fontSmoothing: true,
    tabSize: false,
    textIndent: false,
    verticalAlign: false,
    opacity: true,
    boxShadow: true,
    boxShadowColor: false,
    outlineWidth: true,
    outlineColor: true,
    outlineStyle: true,
    outlineOffset: true,
    ringWidth: true,
    ringColor: true,
    ringOpacity: true,
    ringOffsetWidth: true,
    ringOffsetColor: true,
    blur: true,
    brightness: false,
    contrast: false,
    dropShadow: false,
    grayscale: false,
    hueRotate: false,
    invert: false,
    saturate: false,
    sepia: false,
    filter: true,
    backdropBlur: true,
    backdropBrightness: false,
    backdropContrast: false,
    backdropGrayscale: false,
    backdropHueRotate: false,
    backdropInvert: false,
    backdropOpacity: true,
    backdropSaturate: false,
    backdropSepia: false,
    backdropFilter: true,
    transitionProperty: true,
    transitionDelay: true,
    transitionDuration: true,
    transitionTimingFunction: true,
    willChange: false,
    content: true,
  },
};