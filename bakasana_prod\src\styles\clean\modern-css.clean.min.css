@supports (container-type:inline-size){.card-container{container-type:inline-size;container-name:card}@container card (max-width:300px){.card-content{padding:1rem;flex-direction:column}.card-image{width:100%;height:150px}}@container card (min-width:301px) and (max-width:500px){.card-content{padding:1.5rem;flex-direction:column}.card-image{width:100%;height:200px}}@container card (min-width:501px){.card-content{padding:2rem;flex-direction:row;align-items:center}.card-image{width:40%;height:250px}}}@supports (grid-template-rows:subgrid){.card-grid-parent{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:2rem}.card-with-subgrid{display:grid;grid-template-rows:auto 1fr auto}.card-grid-container{display:grid;grid-template-rows:subgrid;grid-row:span 3}}@supports (height:100dvh){.hero-dynamic{min-height:100dvh;display:flex;align-items:center;justify-content:center}}@supports not (height:100dvh){.hero-dynamic{height:100vh;min-height:100vh}@media (max-width:768px){.hero-dynamic{height:calc(100vh - env(safe-area-inset-bottom));min-height:calc(100vh - env(safe-area-inset-bottom))}}}@supports (animation-timeline:scroll()){@keyframes fadeInOnScroll{from{opacity:0;transform:translateY(50px)}to{opacity:1;transform:translateY(0)}}@keyframes parallaxMove{from{transform:translateY(0)}to{transform:translateY(-100px)}}}