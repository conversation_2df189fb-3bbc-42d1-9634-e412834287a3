:root{--duration-instant:150ms;--duration-quick:250ms;--duration-medium:350ms;--duration-slow:500ms;--duration-extended:750ms;--ease-smooth:cubic-bezier(.25,.46,.45,.94);--ease-bounce:cubic-bezier(.68,-.55,.265,1.55);--ease-swift:cubic-bezier(.4,0,.2,1);--ease-elastic:cubic-bezier(.175,.885,.32,1.275);--ease-premium:cubic-bezier(.16,1,.3,1);--origin-center:center center;--origin-top:center top;--origin-bottom:center bottom;--origin-left:left center;--origin-right:right center;--scale-hover:1.02;--scale-active:.98;--scale-focus:1.01}.btn-primary{position:relative;overflow:hidden;transform:translateZ(0);transition:all var(--duration-quick) var(--ease-premium);will-change:transform,box-shadow,background-color;&::before{content:'';position:absolute;inset:0;background:radial-gradient(circle at var(--mouse-x,50%) var(--mouse-y,50%),rgba(255,255,255,.15) 0%,transparent 70%);opacity:0;transition:opacity var(--duration-quick) var(--ease-premium);pointer-events:none}&:hover::before{opacity:1}&::after{content:'';position:absolute;width:0;height:0;background:radial-gradient(circle,rgba(255,255,255,.3) 0%,transparent 70%);transform:scale(0);pointer-events:none;transition:transform var(--entrance-duration) var(--spring-elastic)}&:focus::after{width:100%;height:100%;transform:scale(1)}}.card-interactive{position:relative;transform:translateZ(0);transition:all var(--duration-medium) var(--ease-premium);will-change:transform,box-shadow}@keyframes shimmer{0%{background-position:-200% 0}100%{background-position:200% 0}}@media (prefers-reduced-motion:reduce){*,*::before,*::after{animation-duration:.01ms !important;animation-iteration-count:1 !important;transition-duration:.01ms !important;scroll-behavior:auto !important}}html{scroll-behavior:smooth}@keyframes fadeInUp{from{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}@keyframes fadeInScale{from{opacity:0;transform:scale(.95)}to{opacity:1;transform:scale(1)}}@keyframes slideInRight{from{opacity:0;transform:translateX(30px)}to{opacity:1;transform:translateX(0)}}@keyframes bounceIn{0%{opacity:0;transform:scale(.3)}50%{opacity:1;transform:scale(1.05)}70%{transform:scale(.9)}100%{opacity:1;transform:scale(1)}}@keyframes float{0%,100%{transform:translateY(0)}50%{transform:translateY(-10px)}}.animate-fade-in-up{animation:fadeInUp .6s ease-out}.animate-fade-in-scale{animation:fadeInScale .4s ease-out}.animate-slide-in-right{animation:slideInRight .5s ease-out}.animate-bounce-in{animation:bounceIn .6s ease-out}.animate-float{animation:float 3s ease-in-out infinite}.unified-card-premium{position:relative;overflow:hidden;transition:all var(--duration-medium) var(--ease-premium);will-change:transform,box-shadow;cursor:pointer}.unified-card-premium:hover{transform:translateY(-6px) scale(var(--scale-hover));box-shadow:0 25px 50px rgba(0,0,0,.12),0 12px 24px rgba(0,0,0,.08)}.unified-card-premium:active{transform:translateY(-3px) scale(var(--scale-active));transition-duration:var(--duration-instant)}.unified-card-premium:focus-visible{outline:none;box-shadow:0 0 0 3px rgba(196,153,107,.3),0 25px 50px rgba(0,0,0,.12)}.btn-ripple{position:relative;overflow:hidden}.btn-ripple::before{content:'';position:absolute;top:50%;left:50%;width:0;height:0;background:rgba(255,255,255,.3);border-radius:50%;transform:translate(-50%,-50%);transition:width var(--duration-medium) var(--ease-elastic),height var(--duration-medium) var(--ease-elastic)}.btn-ripple:active::before{width:300px;height:300px}