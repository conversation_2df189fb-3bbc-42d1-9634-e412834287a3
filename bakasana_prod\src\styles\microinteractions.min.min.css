:root{--duration-instant:150ms;--duration-quick:250ms;--duration-medium:350ms;--duration-slow:500ms;--duration-extended:750ms;--ease-smooth:cubic-bezier(.25,.46,.45,.94);--ease-bounce:cubic-bezier(.68,-.55,.265,1.55);--ease-swift:cubic-bezier(.4,0,.2,1);--ease-elastic:cubic-bezier(.175,.885,.32,1.275);--ease-premium:cubic-bezier(.16,1,.3,1);--origin-center:center center;--origin-top:center top;--origin-bottom:center bottom;--origin-left:left center;--origin-right:right center;--scale-hover:1.02;--scale-active:.98;--scale-focus:1.01}.btn-primary{position:relative;overflow:hidden;transform:translateZ(0);transition:all var(--duration-quick) var(--ease-premium);will-change:transform,box-shadow,background-color;&:hover{background:linear-gradient(135deg,#C4996B 0%,#B8935C 100%);box-shadow:0 10px 25px rgba(184,147,92,.25),0 6px 12px rgba(184,147,92,.15);transform:translateY(-2px) scale(var(--scale-hover))}&:active{transform:translateY(0) scale(var(--scale-active));transition-duration:var(--duration-instant)}&::before{content:'';position:absolute;inset:0;background:radial-gradient(circle at var(--mouse-x,50%) var(--mouse-y,50%),rgba(255,255,255,.15) 0%,transparent 70%);opacity:0;transition:opacity var(--duration-quick) var(--ease-premium);pointer-events:none}&:focus-visible{outline:none;box-shadow:0 0 0 3px rgba(196,153,107,.3),0 0 0 6px rgba(196,153,107,.1);transform:scale(var(--scale-focus))}&:hover::before{opacity:1}&::after{content:'';position:absolute;width:0;height:0;background:radial-gradient(circle,rgba(255,255,255,.3) 0%,transparent 70%);transform:scale(0);pointer-events:none;transition:transform var(--entrance-duration) var(--spring-elastic)}&:focus::after{width:100%;height:100%;transform:scale(1)}}.card-interactive{position:relative;transform:translateZ(0);transition:all var(--duration-medium) var(--ease-premium);will-change:transform,box-shadow;&:hover{box-shadow:0 20px 40px rgba(42,39,36,.08),0 10px 20px rgba(42,39,36,.06);transform:translateY(-8px) scale(1.01)}&.tilt-active{transition:transform var(--micro-duration) var(--spring-smooth)}&:focus-within{outline:none;box-shadow:0 0 0 2px rgba(184,147,92,.2),0 0 0 4px rgba(184,147,92,.1),0 20px 40px rgba(42,39,36,.08)}}.link-enhanced{position:relative;color:var(--temple-gold);text-decoration:none;overflow:hidden;transition:color var(--standard-duration) var(--spring-smooth);&::after{position:absolute;bottom:-2px;left:0;width:0;height:2px;background:linear-gradient(90deg,var(--temple-gold),var(--golden-amber));transition:width var(--standard-duration) var(--spring-smooth);content:''}&:hover::after{width:100%}&::before{content:'';position:absolute;inset:0;background:linear-gradient( 45deg,transparent 30%,rgba(255,255,255,.3) 50%,transparent 70% );transform:translateX(-100%);transition:transform var(--entrance-duration) var(--spring-smooth)}}.form-field{position:relative;input,textarea{transition:all var(--standard-duration) var(--spring-smooth);will-change:border-color,box-shadow,background-color;&:focus{border-color:var(--temple-gold);background-color:rgba(253,252,248,.8);box-shadow:0 0 0 3px rgba(184,147,92,.1),0 4px 12px rgba(184,147,92,.08);outline:none}&:invalid{border-color:#e74c3c;box-shadow:0 0 0 3px rgba(231,76,60,.1)}}label{position:absolute;left:12px;top:50%;transform:translateY(-50%);color:var(--stone);pointer-events:none;transition:all var(--standard-duration) var(--spring-smooth);background:linear-gradient(to bottom,transparent 0%,transparent 40%,var(--sanctuary) 50%,var(--sanctuary) 100%);padding:0 4px}input:focus+label,input:not(:placeholder-shown)+label{top:0;color:var(--temple-gold);font-size:12px;transform:translateY(-50%)}}.reveal-on-scroll{opacity:0;transform:translateY(30px);transition:all var(--entrance-duration) var(--spring-smooth);will-change:opacity,transform;&.revealed{transform:translateY(0);opacity:1}}.reveal-stagger{opacity:0;transform:translateY(20px);transition:all var(--entrance-duration) var(--spring-smooth);will-change:opacity,transform;&.revealed{transform:translateY(0);opacity:1}}.reveal-stagger:nth-child(1){transition-delay:0s}.reveal-stagger:nth-child(2){transition-delay:.1s}.reveal-stagger:nth-child(3){transition-delay:.2s}.reveal-stagger:nth-child(4){transition-delay:.3s}.reveal-stagger:nth-child(5){transition-delay:.4s}.magnetic-element{position:relative;cursor:none;transition:transform var(--micro-duration) var(--spring-smooth);will-change:transform}.cursor-follower{position:fixed;width:20px;height:20px;background:var(--temple-gold);pointer-events:none;z-index:9999;transition:transform var(--micro-duration) var(--spring-smooth);will-change:transform;&.expanded{background:rgba(184,147,92,.3);transform:scale(2)}}.glass-card{background:rgba(253,252,248,.7);backdrop-filter:blur(20px);-webkit-backdrop-filter:blur(20px);border:1px solid rgba(255,255,255,.18);transition:all var(--standard-duration) var(--spring-smooth);will-change:background,backdrop-filter;&:hover{background:rgba(253,252,248,.9);-webkit-backdrop-filter:blur(30px);backdrop-filter:blur(30px)}}.loading-shimmer{position:relative;overflow:hidden;background:linear-gradient( 90deg,var(--whisper) 0%,rgba(255,255,255,.8) 50%,var(--whisper) 100% );background-size:200% 100%;animation:shimmer 2s infinite}@keyframes shimmer{0%{background-position:-200% 0}100%{background-position:200% 0}}.parallax-container{overflow:hidden;position:relative}.parallax-layer{position:absolute;transition:transform var(--micro-duration) linear;inset:0;will-change:transform}.parallax-slow{transform:translateY(var(--parallax-slow,0))}.parallax-medium{transform:translateY(var(--parallax-medium,0))}.parallax-fast{transform:translateY(var(--parallax-fast,0))}@media (prefers-reduced-motion:reduce){*,*::before,*::after{animation-duration:.01ms !important;animation-iteration-count:1 !important;scroll-behavior:auto !important;transition-duration:.01ms !important}}.focus-visible{outline:2px solid var(--temple-gold);outline-offset:2px}html{scroll-behavior:smooth}.will-change-transform{will-change:transform}.will-change-opacity{will-change:opacity}.gpu-accelerated{transform:translateZ(0);backface-visibility:hidden;perspective:1000px}@keyframes fadeInUp{from{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes bounceIn{50%{transform:scale(1.05);opacity:1}70%{transform:scale(.9)}}@keyframes float{0%,100%{transform:translateY(0)}}.animate-fade-in-up{animation:fadeInUp .6s ease-out}.animate-fade-in-scale{animation:fadeInScale .4s ease-out}.animate-slide-in-right{animation:slideInRight .5s ease-out}.animate-bounce-in{animation:bounceIn .6s ease-out}.animate-float{animation:float 3s ease-in-out infinite}.touch-feedback{position:relative;overflow:hidden;&::after{position:absolute;top:50%;left:50%;width:0;height:0;background:radial-gradient(circle,rgba(255,255,255,.3) 0%,transparent 70%);transform:translate(-50%,-50%);transition:width .3s ease,height .3s ease;content:'';pointer-events:none}&:active::after{width:200px;height:200px}}@supports (backdrop-filter:blur(10px)){.enhanced-glass{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}}.unified-card-premium{position:relative;overflow:hidden;transition:all var(--duration-medium) var(--ease-premium);will-change:transform,box-shadow;cursor:pointer}.unified-card-premium:hover{box-shadow:0 25px 50px rgba(0,0,0,.12),0 12px 24px rgba(0,0,0,.08);transform:translateY(-6px) scale(var(--scale-hover))}.unified-card-premium:active{transform:translateY(-3px) scale(var(--scale-active));transition-duration:var(--duration-instant)}.unified-card-premium:focus-visible{box-shadow:0 0 0 3px rgba(196,153,107,.3),0 25px 50px rgba(0,0,0,.12);outline:none}.link-premium{position:relative;transition:all var(--duration-quick) var(--ease-premium);text-decoration:none;display:inline-block}.link-premium::after{position:absolute;bottom:-2px;left:0;width:0;height:2px;background:linear-gradient(90deg,var(--golden),var(--sunset));transition:width var(--duration-medium) var(--ease-elastic);content:''}.link-premium:hover{color:var(--golden);transform:translateY(-1px)}.link-premium:hover::after{width:100%}.link-premium:focus-visible{color:var(--golden);transform:scale(var(--scale-focus));outline:none}.btn-ripple{position:relative;overflow:hidden}.btn-ripple::before{position:absolute;top:50%;left:50%;width:0;height:0;border-radius:50%;background:rgba(255,255,255,.3);transform:translate(-50%,-50%);transition:width var(--duration-medium) var(--ease-elastic),height var(--duration-medium) var(--ease-elastic);content:''}.btn-ripple:active::before{width:300px;height:300px}@supports (container-type:inline-size){.container-animations{container-type:inline-size}@container (max-width:400px){.container-animations .animate-fade-in-up{animation-duration:var(--duration-medium)}}}