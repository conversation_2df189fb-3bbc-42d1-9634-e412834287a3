{"name": "baka<PERSON>a-travel-blog", "version": "0.1.0", "private": true, "homepage": "https://bakasana-travel.blog", "description": "Retreaty jogowe na Bali z Julią Jakubowicz - transformacyjne podróże łączące praktykę jogi z pięknem Bali", "keywords": ["joga", "bali", "retreat", "j<PERSON><PERSON>", "fizjoterapia", "wellness", "mindfulness"], "author": "<PERSON>", "scripts": {"dev": "next dev -p 3002", "build": "next build", "build:analyze": "cross-env ANALYZE=true next build", "start": "next start -p 3000", "start:prod": "npm run build && npm run start", "lint": "npx eslint src --fix", "optimize-images": "node scripts/optimize-images.js", "compress-images": "node scripts/compress-images.js", "compress:jpeg": "npx imagemin public/images/**/*.{jpg,jpeg} --out-dir=public/images/optimized --plugin=mozjpeg", "compress:png": "npx imagemin public/images/**/*.png --out-dir=public/images/optimized --plugin=pngquant", "convert:webp": "npx imagemin public/images/**/*.{jpg,jpeg,png} --out-dir=public/images/webp --plugin=webp", "convert:avif": "npx imagemin public/images/**/*.{jpg,jpeg,png} --out-dir=public/images/avif --plugin=avif", "images:full-optimize": "npm run compress-images && npm run optimize-images", "images:analyze": "node scripts/check-image-sizes.js", "clean": "node scripts/safe-clean.js", "dev:clean": "npm run clean && npm run dev", "build:clean": "npm run clean && npm run build", "postbuild": "next-sitemap", "sanity:dev": "sanity dev", "sanity:build": "sanity build", "sanity:deploy": "sanity deploy", "premium:switch": "node scripts/switch-to-premium.js", "premium:restore": "node scripts/restore-original.js", "premium:dev": "next dev -p 3002", "premium:build": "next build", "migrate:unified": "node scripts/migrate-to-unified.js", "style:check": "echo 'Sprawdzanie konsekwentności stylów...' && node scripts/style-checker.js", "fix:imports": "node scripts/fix-import-errors.js", "standardize:code": "node scripts/standardize-code.js", "premium:start": "next start -p 3002", "pwa:screenshots": "node scripts/generate-pwa-screenshots.js", "pwa:setup": "npm run pwa:screenshots && npm run build", "enterprise:setup": "npm run pwa:setup && npm run build:analyze", "performance:monitor": "node scripts/performance-monitor.js", "performance:optimize": "npm run optimize-images && node scripts/critical-css.js && node scripts/minify-assets.js", "performance:audit": "node scripts/lighthouse-audit.js", "performance:full": "npm run performance:optimize && npm run build && npm run performance:audit", "mobile:test": "npm run pwa:screenshots && npm run dev", "mobile:audit": "node scripts/mobile-audit.js", "security:check": "npm audit && echo 'Security audit completed'", "ux:validate": "npm run mobile:audit && npm run security:check", "css:purge": "node scripts/purge-css.js", "css:analyze": "node scripts/analyze-css.js", "css:optimize": "npm run css:purge && npm run css:analyze", "css:apply": "node scripts/apply-clean-css.js", "css:restore": "node scripts/apply-clean-css.js restore", "css:full-optimize": "npm run css:purge && npm run css:apply", "z-index:check": "node scripts/check-z-index.js", "z-index:analyze": "node scripts/analyze-css-z-index.js", "z-index:browser": "echo 'Sko<PERSON><PERSON><PERSON> scripts/browser-z-index-checker.js do konsoli przeglądarki'", "z-index:fix": "node scripts/fix-z-index.js", "z-index:restore": "node scripts/fix-z-index.js restore", "z-index:full": "npm run z-index:analyze && npm run z-index:fix && npm run z-index:analyze", "css:duplicates": "node scripts/find-css-duplicates.js", "css:sort": "node scripts/sort-css-properties.js", "css:cleanup": "node scripts/css-cleanup.js", "css:clean-all": "npm run css:duplicates && npm run css:cleanup && npm run css:optimize", "lint:check": "npx eslint src", "format": "npx prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "format:check": "npx prettier --check \"src/**/*.{js,jsx,ts,tsx}\"", "code:fix": "npm run lint && npm run format", "code:check": "npm run lint:check && npm run format:check", "minify:simple": "node scripts/simple-minify.js", "minify:css": "node scripts/simple-minify.js", "minify:all": "npm run minify:simple && npm run minify:assets", "build:minified": "npm run build && npm run minify:all", "minify:assets": "node scripts/minify-assets.js", "validate:html": "node scripts/html-seo-validation.js", "validate:schema": "node scripts/structured-data-validator.js", "validate:all": "npm run validate:html && npm run validate:schema", "fix:html": "node scripts/quick-html-fixes.js"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-slot": "^1.2.0", "@sentry/nextjs": "^8.0.0", "@tailwindcss/typography": "^0.5.16", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "aos": "^2.3.4", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "framer-motion": "^12.12.2", "jose": "^6.0.6", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "mapbox-gl": "^3.13.0", "mixpanel-browser": "^2.47.0", "moment": "^2.30.1", "next": "^15.3.2", "next-sitemap": "^4.2.3", "react": "^18.3.1", "react-big-calendar": "^1.19.4", "react-cookie-consent": "^9.0.0", "react-dom": "^18.3.1", "react-icons": "^5.0.1", "react-intersection-observer": "^9.15.1", "react-lazy-load-image-component": "^1.6.3", "react-map-gl": "^8.0.4", "sharp": "^0.34.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "web-vitals": "^3.5.2", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.27.0", "@next/bundle-analyzer": "^15.0.0", "@types/node": "^20.12.7", "@types/react": "19.1.6", "autoprefixer": "^10.4.21", "compression-webpack-plugin": "^11.1.0", "cross-env": "^7.0.3", "csscomb": "^4.3.0", "cssnano": "^7.0.7", "eslint": "^9.31.0", "eslint-config-next": "^15.4.0-canary.51", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.3", "glob": "^11.0.3", "html-validate": "^9.7.1", "imagemin-avif": "^0.1.6", "imagemin-cli": "^8.0.0", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^10.0.0", "imagemin-webp": "^8.0.0", "postcss": "^8.5.3", "prettier": "^3.6.2", "puppeteer": "^22.1.0", "purgecss": "^7.0.2", "rimraf": "^5.0.5", "schema-dts": "^1.1.5", "tailwindcss": "^3.4.17", "typescript": "^5.4.5"}}