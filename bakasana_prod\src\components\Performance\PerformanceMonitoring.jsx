'use client';

import { useEffect, useCallback, useState } from 'react';
import { usePathname } from 'next/navigation';

export default function PerformanceMonitoring() {
  const pathname = usePathname();
  const [performanceData, setPerformanceData] = useState({});

  // Core Web Vitals tracking
  const trackWebVitals = useCallback(() => {
    if (typeof window === 'undefined') return;

    // Track Largest Contentful Paint (LCP)
    const trackLCP = () => {
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        const lastEntry = entries[entries.length - 1];
        
        const lcpValue = lastEntry.startTime;
        
        // Send to analytics
        if (window.gtag) {
          window.gtag('event', 'web_vitals', {
            event_category: 'Web Vitals',
            event_label: 'LCP',
            value: Math.round(lcpValue),
            custom_parameter_1: pathname,
            non_interaction: true
          });
        }

        if (window.mixpanel) {
          window.mixpanel.track('Web Vitals - LCP', {
            value: lcpValue,
            page: pathname,
            rating: lcpValue <= 2500 ? 'good' : lcpValue <= 4000 ? 'needs_improvement' : 'poor'
          });
        }

        setPerformanceData(prev => ({ ...prev, lcp: lcpValue }));
      }).observe({ entryTypes: ['largest-contentful-paint'] });
    };

    // Track First Input Delay (FID)
    const trackFID = () => {
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        entries.forEach((entry) => {
          const fidValue = entry.processingStart - entry.startTime;
          
          if (window.gtag) {
            window.gtag('event', 'web_vitals', {
              event_category: 'Web Vitals',
              event_label: 'FID',
              value: Math.round(fidValue),
              custom_parameter_1: pathname,
              non_interaction: true
            });
          }

          if (window.mixpanel) {
            window.mixpanel.track('Web Vitals - FID', {
              value: fidValue,
              page: pathname,
              rating: fidValue <= 100 ? 'good' : fidValue <= 300 ? 'needs_improvement' : 'poor'
            });
          }

          setPerformanceData(prev => ({ ...prev, fid: fidValue }));
        });
      }).observe({ entryTypes: ['first-input'] });
    };

    // Track Cumulative Layout Shift (CLS)
    const trackCLS = () => {
      let clsValue = 0;
      let clsEntries = [];

      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        
        entries.forEach((entry) => {
          if (!entry.hadRecentInput) {
            clsEntries.push(entry);
            clsValue += entry.value;
          }
        });

        if (window.gtag) {
          window.gtag('event', 'web_vitals', {
            event_category: 'Web Vitals',
            event_label: 'CLS',
            value: Math.round(clsValue * 1000),
            custom_parameter_1: pathname,
            non_interaction: true
          });
        }

        if (window.mixpanel) {
          window.mixpanel.track('Web Vitals - CLS', {
            value: clsValue,
            page: pathname,
            rating: clsValue <= 0.1 ? 'good' : clsValue <= 0.25 ? 'needs_improvement' : 'poor'
          });
        }

        setPerformanceData(prev => ({ ...prev, cls: clsValue }));
      }).observe({ entryTypes: ['layout-shift'] });
    };

    // Track Time to First Byte (TTFB)
    const trackTTFB = () => {
      const navigationEntry = performance.getEntriesByType('navigation')[0];
      if (navigationEntry) {
        const ttfbValue = navigationEntry.responseStart - navigationEntry.requestStart;
        
        if (window.gtag) {
          window.gtag('event', 'web_vitals', {
            event_category: 'Web Vitals',
            event_label: 'TTFB',
            value: Math.round(ttfbValue),
            custom_parameter_1: pathname,
            non_interaction: true
          });
        }

        if (window.mixpanel) {
          window.mixpanel.track('Web Vitals - TTFB', {
            value: ttfbValue,
            page: pathname,
            rating: ttfbValue <= 800 ? 'good' : ttfbValue <= 1800 ? 'needs_improvement' : 'poor'
          });
        }

        setPerformanceData(prev => ({ ...prev, ttfb: ttfbValue }));
      }
    };

    // Initialize tracking
    trackLCP();
    trackFID();
    trackCLS();
    trackTTFB();
  }, [pathname]);

  // Track resource loading performance
  const trackResourcePerformance = useCallback(() => {
    if (typeof window === 'undefined') return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach((entry) => {
        if (entry.entryType === 'resource') {
          const resourceData = {
            name: entry.name,
            duration: entry.duration,
            size: entry.transferSize,
            type: entry.initiatorType,
            page: pathname
          };

          // Track slow resources
          if (entry.duration > 1000) {
            if (window.gtag) {
              window.gtag('event', 'slow_resource', {
                event_category: 'Performance',
                event_label: entry.initiatorType,
                value: Math.round(entry.duration),
                custom_parameter_1: pathname
              });
            }

            if (window.mixpanel) {
              window.mixpanel.track('Slow Resource Load', resourceData);
            }
          }
        }
      });
    });

    observer.observe({ entryTypes: ['resource'] });

    return () => observer.disconnect();
  }, [pathname]);

  // Track user interactions and their performance impact
  const trackInteractionPerformance = useCallback(() => {
    if (typeof window === 'undefined') return;

    let interactionCount = 0;

    const trackInteraction = (eventType) => {
      return (event) => {
        interactionCount++;
        const startTime = performance.now();

        // Use requestIdleCallback to measure impact
        requestIdleCallback(() => {
          const endTime = performance.now();
          const duration = endTime - startTime;

          if (duration > 50) { // Track interactions that take longer than 50ms
            if (window.gtag) {
              window.gtag('event', 'slow_interaction', {
                event_category: 'Performance',
                event_label: eventType,
                value: Math.round(duration),
                custom_parameter_1: pathname
              });
            }

            if (window.mixpanel) {
              window.mixpanel.track('Slow Interaction', {
                event_type: eventType,
                duration: duration,
                page: pathname,
                interaction_count: interactionCount
              });
            }
          }
        });
      };
    };

    // Track various interaction types
    document.addEventListener('click', trackInteraction('click'));
    document.addEventListener('scroll', trackInteraction('scroll'));
    document.addEventListener('keydown', trackInteraction('keydown'));

    return () => {
      document.removeEventListener('click', trackInteraction('click'));
      document.removeEventListener('scroll', trackInteraction('scroll'));
      document.removeEventListener('keydown', trackInteraction('keydown'));
    };
  }, [pathname]);

  // Track memory usage
  const trackMemoryUsage = useCallback(() => {
    if (typeof window === 'undefined' || !performance.memory) return;

    const memoryInfo = performance.memory;
    const memoryData = {
      used: memoryInfo.usedJSHeapSize,
      total: memoryInfo.totalJSHeapSize,
      limit: memoryInfo.jsHeapSizeLimit,
      page: pathname
    };

    // Track if memory usage is high
    const memoryUsagePercent = (memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit) * 100;
    
    if (memoryUsagePercent > 80) {
      if (window.gtag) {
        window.gtag('event', 'high_memory_usage', {
          event_category: 'Performance',
          event_label: 'Memory',
          value: Math.round(memoryUsagePercent),
          custom_parameter_1: pathname
        });
      }

      if (window.mixpanel) {
        window.mixpanel.track('High Memory Usage', {
          ...memoryData,
          usage_percent: memoryUsagePercent
        });
      }
    }

    setPerformanceData(prev => ({ ...prev, memory: memoryData }));
  }, [pathname]);

  // Send performance data to external monitoring services
  const sendToExternalServices = useCallback(async (data) => {
    // This would typically be called from a server-side API route
    // to avoid exposing API keys in the client
    
    try {
      // Send to your backend API which then forwards to monitoring services
      await fetch('/api/performance/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          page: pathname,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          connection: navigator.connection ? {
            effectiveType: navigator.connection.effectiveType,
            downlink: navigator.connection.downlink,
            rtt: navigator.connection.rtt
          } : null
        }),
      });
    } catch (error) {
      console.error('Failed to send performance data:', error);
      
      // Track the error
      if (window.trackRetreatError) {
        window.trackRetreatError(error, {
          component: 'PerformanceMonitoring',
          action: 'sendToExternalServices'
        });
      }
    }
  }, [pathname]);

  // Initialize performance monitoring
  useEffect(() => {
    const cleanup = [];

    // Start tracking
    trackWebVitals();
    cleanup.push(trackResourcePerformance());
    cleanup.push(trackInteractionPerformance());
    
    // Track memory usage periodically
    const memoryInterval = setInterval(trackMemoryUsage, 30000); // Every 30 seconds
    cleanup.push(() => clearInterval(memoryInterval));

    // Send initial performance data after page load
    const sendInitialData = setTimeout(() => {
      sendToExternalServices(performanceData);
    }, 5000);
    cleanup.push(() => clearTimeout(sendInitialData));

    return () => {
      cleanup.forEach(fn => typeof fn === 'function' && fn());
    };
  }, [trackWebVitals, trackResourcePerformance, trackInteractionPerformance, trackMemoryUsage, sendToExternalServices, performanceData]);

  // Send performance data when component unmounts or page changes
  useEffect(() => {
    return () => {
      if (Object.keys(performanceData).length > 0) {
        sendToExternalServices(performanceData);
      }
    };
  }, [performanceData, sendToExternalServices]);

  // This component doesn't render anything visible
  return null;
}
