{"version": 3, "file": "jest.js", "sources": ["../../src/jest/jest.ts"], "sourcesContent": ["import \"./augmentation\";\n\nimport {\n\ttoBeValid,\n\ttoBeInvalid,\n\ttoHTMLValidate,\n\ttoHaveError,\n\ttoHaveErrors,\n\ttoMatchCodeframe,\n\ttoMatchInlineCodeframe,\n} from \"./matchers\";\nimport { diff } from \"./utils\";\n\nexpect.extend({\n\ttoBeValid: toBeValid(),\n\ttoBeInvalid: toBeInvalid(),\n\ttoHTMLValidate: toHTMLValidate(expect, diff),\n\ttoHaveError: toHaveError(expect, diff),\n\ttoHaveErrors: toHaveErrors(expect, diff),\n\ttoMatchCodeframe: toMatchCodeframe(),\n\ttoMatchInlineCodeframe: toMatchInlineCodeframe(),\n});\n"], "names": ["toBeValid", "toBeInvalid", "toHTMLValidate", "diff", "toHaveError", "toHaveErrors", "toMatchCodeframe", "toMatchInlineCodeframe"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAaA,MAAA,CAAO,MAAA,CAAO;AAAA,EACb,WAAWA,wBAAA,EAAU;AAAA,EACrB,aAAaC,wBAAA,EAAY;AAAA,EACzB,cAAA,EAAgBC,wBAAA,CAAe,MAAA,EAAQC,aAAI,CAAA;AAAA,EAC3C,WAAA,EAAaC,wBAAA,CAAY,MAAA,EAAQD,aAAI,CAAA;AAAA,EACrC,YAAA,EAAcE,sBAAA,CAAa,MAAA,EAAQF,aAAI,CAAA;AAAA,EACvC,kBAAkBG,gCAAA,EAAiB;AAAA,EACnC,wBAAwBC,8BAAA;AACzB,CAAC,CAAA;;"}