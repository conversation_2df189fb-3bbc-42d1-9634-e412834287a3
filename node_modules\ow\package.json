{"name": "ow", "version": "2.0.0", "description": "Function argument validation for humans", "license": "MIT", "repository": "sindresorhus/ow", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./dev-only": {"types": "./dist/index.d.ts", "default": "./dev-only.js"}}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && NODE_OPTIONS='--import=tsx/esm' c8 ava", "docs": "typedoc source/index.ts", "build": "del-cli dist && tsc", "prepare": "npm run build", "postpublish": "npm run docs && gh-pages --dist docs --no-history --message \"Deploy documentation\"", "example": "npm run build && node example.js"}, "files": ["dist", "dev-only.js"], "keywords": ["type", "types", "check", "checking", "guard", "guards", "assert", "assertion", "predicate", "predicates", "is", "validate", "validation", "utility", "util", "typeof", "instanceof", "object"], "dependencies": {"@sindresorhus/is": "^6.3.0", "callsites": "^4.1.0", "dot-prop": "^8.0.2", "environment": "^1.0.0", "fast-equals": "^5.0.1", "is-identifier": "^1.0.0"}, "devDependencies": {"@sindresorhus/tsconfig": "^5.0.0", "@types/node": "^20.12.8", "ava": "^6.1.2", "c8": "^9.1.0", "del-cli": "^5.1.0", "expect-type": "^0.19.0", "gh-pages": "^6.1.1", "tsx": "^4.9.1", "typedoc": "^0.25.13", "typescript": "^5.4.5", "xo": "^0.58.0"}, "browser": {"./dist/utils/infer-label.js": "./dist/utils/infer-label.browser.js"}, "xo": {"ignores": ["example.js", "dev-only.js", "source/utils/infer-label.browser.ts"], "rules": {"no-useless-return": "off", "@typescript-eslint/explicit-function-return-type": "error", "@typescript-eslint/ban-types": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/restrict-template-expressions": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/no-unsafe-argument": "off"}}, "ava": {"files": ["test/**", "!test/fixtures/**"], "extensions": {"ts": "module"}, "workerThreads": false}, "c8": {"reporter": ["text", "lcov"]}}