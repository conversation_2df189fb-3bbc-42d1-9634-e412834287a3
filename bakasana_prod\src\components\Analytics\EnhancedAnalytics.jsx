'use client';

import { useEffect, useCallback } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import Script from 'next/script';

// Environment variables
const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;
const HOTJAR_ID = process.env.NEXT_PUBLIC_HOTJAR_ID;
const SENTRY_DSN = process.env.NEXT_PUBLIC_SENTRY_DSN;
const MIXPANEL_TOKEN = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN;
const FB_PIXEL_ID = process.env.NEXT_PUBLIC_FB_PIXEL_ID;

export default function EnhancedAnalytics() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Track page views
  const trackPageView = useCallback((url) => {
    // Google Analytics 4
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', GA_MEASUREMENT_ID, {
        page_path: url,
        custom_map: {
          custom_parameter_1: 'retreat_interest',
          custom_parameter_2: 'user_journey_stage'
        }
      });

      // Enhanced ecommerce tracking for retreat bookings
      window.gtag('event', 'page_view', {
        page_title: document.title,
        page_location: window.location.href,
        page_path: url,
        content_group1: getContentGroup(url),
        content_group2: getUserJourneyStage(url)
      });
    }

    // Mixpanel
    if (typeof window !== 'undefined' && window.mixpanel) {
      window.mixpanel.track('Page View', {
        page: url,
        title: document.title,
        referrer: document.referrer,
        content_group: getContentGroup(url),
        user_journey_stage: getUserJourneyStage(url)
      });
    }

    // Facebook Pixel
    if (typeof window !== 'undefined' && window.fbq) {
      window.fbq('track', 'PageView');
    }

    // Hotjar
    if (typeof window !== 'undefined' && window.hj) {
      window.hj('stateChange', url);
    }
  }, []);

  // Get content group for analytics
  const getContentGroup = (url) => {
    if (url.includes('/retreaty')) return 'Retreats';
    if (url.includes('/blog')) return 'Blog';
    if (url.includes('/o-mnie')) return 'About';
    if (url.includes('/kontakt')) return 'Contact';
    if (url.includes('/rezerwacja')) return 'Booking';
    if (url.includes('/galeria')) return 'Gallery';
    if (url === '/') return 'Homepage';
    return 'Other';
  };

  // Get user journey stage
  const getUserJourneyStage = (url) => {
    if (url === '/') return 'Awareness';
    if (url.includes('/retreaty') || url.includes('/program')) return 'Consideration';
    if (url.includes('/rezerwacja')) return 'Conversion';
    if (url.includes('/kontakt')) return 'Support';
    return 'Engagement';
  };

  // Track page views on route change
  useEffect(() => {
    const url = pathname + (searchParams.toString() ? `?${searchParams.toString()}` : '');
    trackPageView(url);
  }, [pathname, searchParams, trackPageView]);

  return (
    <>
      {/* Google Analytics 4 */}
      {GA_MEASUREMENT_ID && (
        <>
          <Script
            src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
            strategy="afterInteractive"
            onLoad={() => {
              console.log('Google Analytics loaded');
            }}
            onError={(e) => {
              console.error('Failed to load Google Analytics:', e);
            }}
          />
          <Script
            id="google-analytics"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                
                gtag('config', '${GA_MEASUREMENT_ID}', {
                  page_path: window.location.pathname,
                  send_page_view: false,
                  custom_map: {
                    'custom_parameter_1': 'retreat_interest',
                    'custom_parameter_2': 'user_journey_stage'
                  }
                });

                // Enhanced ecommerce setup
                gtag('config', '${GA_MEASUREMENT_ID}', {
                  currency: 'PLN',
                  country: 'PL',
                  language: 'pl'
                });

                // Track retreat interest events
                window.trackRetreatInterest = function(retreatName, action) {
                  gtag('event', action, {
                    event_category: 'Retreat Interest',
                    event_label: retreatName,
                    custom_parameter_1: retreatName,
                    value: action === 'booking_started' ? 1 : 0
                  });
                };

                // Track form interactions
                window.trackFormInteraction = function(formName, action) {
                  gtag('event', action, {
                    event_category: 'Form Interaction',
                    event_label: formName,
                    custom_parameter_2: 'form_engagement'
                  });
                };
              `,
            }}
          />
        </>
      )}

      {/* Hotjar */}
      {HOTJAR_ID && (
        <Script
          id="hotjar"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function(h,o,t,j,a,r){
                h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                h._hjSettings={hjid:${HOTJAR_ID},hjsv:6};
                a=o.getElementsByTagName('head')[0];
                r=o.createElement('script');r.async=1;
                r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                a.appendChild(r);
              })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');

              // Custom Hotjar events for retreat business
              window.hjTrackRetreatEvent = function(eventName, properties) {
                if (window.hj) {
                  window.hj('event', eventName);
                  if (properties) {
                    window.hj('identify', properties.userId || 'anonymous', properties);
                  }
                }
              };

              // Track retreat-specific user behavior
              window.hjTrackRetreatInteraction = function(retreatName, interaction) {
                if (window.hj) {
                  window.hj('event', 'retreat_interaction');
                  window.hj('identify', 'anonymous', {
                    retreat_name: retreatName,
                    interaction_type: interaction,
                    page: window.location.pathname
                  });
                }
              };
            `,
          }}
          onLoad={() => {
            console.log('Hotjar loaded');
          }}
          onError={(e) => {
            console.error('Failed to load Hotjar:', e);
          }}
        />
      )}

      {/* Enhanced Sentry */}
      {SENTRY_DSN && (
        <Script
          id="sentry"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              !function(e,n,t,r,o,a,s){e[o]=e[o]||function(){(e[o].q=e[o].q||[]).push(arguments)},a=n.createElement(t),s=n.getElementsByTagName(t)[0],a.async=1,a.src=r,s.parentNode.insertBefore(a,s)}(window,document,"script","https://browser.sentry-cdn.com/7.28.1/bundle.min.js","Sentry");
              
              Sentry.init({
                dsn: "${SENTRY_DSN}",
                environment: "${process.env.NODE_ENV}",
                tracesSampleRate: ${process.env.NODE_ENV === 'production' ? '0.1' : '1.0'},
                integrations: [
                  new Sentry.BrowserTracing({
                    tracingOrigins: [window.location.hostname, /^\\//],
                    routingInstrumentation: Sentry.reactRouterV6Instrumentation(
                      React.useEffect,
                      useLocation,
                      useNavigationType,
                      createRoutesFromChildren,
                      matchRoutes
                    ),
                  }),
                  new Sentry.Replay({
                    maskAllText: false,
                    blockAllMedia: false,
                    sampleRate: 0.1,
                    errorSampleRate: 1.0,
                  }),
                ],
                beforeSend(event, hint) {
                  // Filter out non-critical errors in production
                  if (process.env.NODE_ENV === 'production') {
                    const error = hint.originalException;
                    
                    // Skip network errors and chunk loading errors
                    if (error && (
                      error.name === 'ChunkLoadError' ||
                      error.name === 'NetworkError' ||
                      error.message?.includes('Loading chunk') ||
                      error.message?.includes('Loading CSS chunk')
                    )) {
                      return null;
                    }
                  }
                  
                  return event;
                },
                beforeSendTransaction(event) {
                  // Sample transactions in production
                  if (process.env.NODE_ENV === 'production' && Math.random() > 0.1) {
                    return null;
                  }
                  return event;
                },
              });

              // Custom error tracking for retreat business
              window.trackRetreatError = function(error, context) {
                Sentry.withScope((scope) => {
                  scope.setTag('component', 'retreat');
                  scope.setContext('retreat_context', context);
                  Sentry.captureException(error);
                });
              };

              // Track booking errors specifically
              window.trackBookingError = function(error, bookingData) {
                Sentry.withScope((scope) => {
                  scope.setTag('component', 'booking');
                  scope.setLevel('error');
                  scope.setContext('booking_data', {
                    retreat_name: bookingData?.retreatName,
                    step: bookingData?.step,
                    user_id: bookingData?.userId
                  });
                  Sentry.captureException(error);
                });
              };
            `,
          }}
          onLoad={() => {
            console.log('Sentry loaded');
          }}
          onError={(e) => {
            console.error('Failed to load Sentry:', e);
          }}
        />
      )}

      {/* Mixpanel */}
      {MIXPANEL_TOKEN && (
        <Script
          id="mixpanel"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function(c,a){if(!a.__SV){var b=window;try{var d,m,j,k=b.location,f=k.hash;d=function(a,b){return(m=a.match(RegExp(b+"=([^&]*)")))?m[1]:null};f&&d(f,"state")&&(j=JSON.parse(decodeURIComponent(d(f,"state"))),"mpeditor"===j.action&&(b.sessionStorage.setItem("_mpcehash",f),history.replaceState(j.desiredHash||"",c.title,k.pathname+k.search)))}catch(n){}var l,h;window.mixpanel=a;a._i=[];a.init=function(b,d,g){function c(b,i){var a=i.split(".");2==a.length&&(b=b[a[0]],i=a[1]);b[i]=function(){b.push([i].concat(Array.prototype.slice.call(arguments,0)))}}var e=a;"undefined"!==typeof g?e=a[g]=[]:g="mixpanel";e.people=e.people||[];e.toString=function(b){var a="mixpanel";"mixpanel"!==g&&(a+="."+g);b||(a+=" (stub)");return a};e.people.toString=function(){return e.toString(1)+".people (stub)"};l="disable time_event track track_pageview track_links track_forms track_with_groups add_group set_group remove_group register register_once alias unregister identify name_tag set_config reset opt_in_tracking opt_out_tracking has_opted_in_tracking has_opted_out_tracking clear_opt_in_out_tracking start_batch_senders people.set people.set_once people.unset people.increment people.append people.union people.track_charge people.clear_charges people.delete_user people.remove".split(" ");for(h=0;h<l.length;h++)c(e,l[h]);var f="set track_pageview track identify alias people.set people.set_once set_config register register_once".split(" ");for(h=0;h<f.length;h++)e[f[h]]=function(a){return function(){mixpanel.push([a].concat(Array.prototype.slice.call(arguments,0)))}}(f[h])};a._i.push([b,d,g])};a.__SV=1.2;b=c.createElement("script");b.type="text/javascript";b.async=!0;b.src="undefined"!==typeof MIXPANEL_CUSTOM_LIB_URL?MIXPANEL_CUSTOM_LIB_URL:"file:"===c.location.protocol&&"//cdn4.mxpnl.com/libs/mixpanel-2-latest.min.js".match(/^\\/\\//)?"https://cdn4.mxpnl.com/libs/mixpanel-2-latest.min.js":"//cdn4.mxpnl.com/libs/mixpanel-2-latest.min.js";d=c.getElementsByTagName("script")[0];d.parentNode.insertBefore(b,d)}})(document,window.mixpanel||[]);
              
              mixpanel.init("${MIXPANEL_TOKEN}", {
                debug: ${process.env.NODE_ENV === 'development'},
                track_pageview: false,
                persistence: 'localStorage',
                property_blacklist: ['$current_url', '$initial_referrer', '$referrer'],
                ip: false,
                api_host: "https://api-eu.mixpanel.com"
              });

              // Custom retreat tracking functions
              window.trackRetreatEvent = function(eventName, properties) {
                mixpanel.track(eventName, {
                  ...properties,
                  page: window.location.pathname,
                  timestamp: new Date().toISOString(),
                  user_agent: navigator.userAgent,
                  language: navigator.language
                });
              };

              window.identifyRetreatUser = function(userId, properties) {
                mixpanel.identify(userId);
                mixpanel.people.set({
                  ...properties,
                  '$last_seen': new Date(),
                  'website_visitor': true
                });
              };
            `,
          }}
          onLoad={() => {
            console.log('Mixpanel loaded');
          }}
          onError={(e) => {
            console.error('Failed to load Mixpanel:', e);
          }}
        />
      )}

      {/* Facebook Pixel */}
      {FB_PIXEL_ID && (
        <Script
          id="facebook-pixel"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              !function(f,b,e,v,n,t,s)
              {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
              n.callMethod.apply(n,arguments):n.queue.push(arguments)};
              if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
              n.queue=[];t=b.createElement(e);t.async=!0;
              t.src=v;s=b.getElementsByTagName(e)[0];
              s.parentNode.insertBefore(t,s)}(window, document,'script',
              'https://connect.facebook.net/en_US/fbevents.js');
              
              fbq('init', '${FB_PIXEL_ID}');
              fbq('track', 'PageView');

              // Custom events for retreat business
              window.trackRetreatFBEvent = function(eventName, parameters) {
                fbq('track', eventName, parameters);
              };
            `,
          }}
          onLoad={() => {
            console.log('Facebook Pixel loaded');
          }}
          onError={(e) => {
            console.error('Failed to load Facebook Pixel:', e);
          }}
        />
      )}
    </>
  );
}
