globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":{"*":{"id":"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js":{"*":{"id":"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Analytics/LazyAnalytics.jsx":{"*":{"id":"(ssr)/./src/components/Analytics/LazyAnalytics.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ClientOnlyResponsiveChecker.jsx":{"*":{"id":"(ssr)/./src/components/ClientOnlyResponsiveChecker.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ConditionalNavbar.jsx":{"*":{"id":"(ssr)/./src/components/ConditionalNavbar.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/CoreWebVitals.jsx":{"*":{"id":"(ssr)/./src/components/CoreWebVitals.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Footer/index.jsx":{"*":{"id":"(ssr)/./src/components/Footer/index.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/OptimizedBreadcrumbs.jsx":{"*":{"id":"(ssr)/./src/components/OptimizedBreadcrumbs.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/QuickCTA.jsx":{"*":{"id":"(ssr)/./src/components/QuickCTA.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/SEO/ContactStructuredData.jsx":{"*":{"id":"(ssr)/./src/components/SEO/ContactStructuredData.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/SEO/EnhancedStructuredData.jsx":{"*":{"id":"(ssr)/./src/components/SEO/EnhancedStructuredData.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/SmoothScrollProvider.jsx":{"*":{"id":"(ssr)/./src/components/ui/SmoothScrollProvider.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/error.jsx":{"*":{"id":"(ssr)/./src/app/error.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found-bali.jsx":{"*":{"id":"(ssr)/./src/app/not-found-bali.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.jsx":{"*":{"id":"(ssr)/./src/app/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/blog/BlogPageClientContent.jsx":{"*":{"id":"(ssr)/./src/app/blog/BlogPageClientContent.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/UnifiedTypography.jsx":{"*":{"id":"(ssr)/./src/components/ui/UnifiedTypography.jsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\dynamic-bailout-to-csr.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\esm\\shared\\lib\\lazy-dynamic\\dynamic-bailout-to-csr.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\preload-chunks.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\esm\\shared\\lib\\lazy-dynamic\\preload-chunks.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.jsx\",\"import\":\"Cormorant_Garamond\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\"],\"display\":\"swap\",\"variable\":\"--font-cormorant\",\"preload\":true}],\"variableName\":\"cormorant\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.jsx\",\"import\":\"Cormorant_Garamond\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\"],\"display\":\"swap\",\"variable\":\"--font-cormorant\",\"preload\":true}],\"variableName\":\"cormorant\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.jsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\"],\"display\":\"swap\",\"variable\":\"--font-inter\",\"preload\":true}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.jsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\"],\"display\":\"swap\",\"variable\":\"--font-inter\",\"preload\":true}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\LazyAnalytics.jsx":{"id":"(app-pages-browser)/./src/components/Analytics/LazyAnalytics.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ClientOnlyResponsiveChecker.jsx":{"id":"(app-pages-browser)/./src/components/ClientOnlyResponsiveChecker.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ConditionalNavbar.jsx":{"id":"(app-pages-browser)/./src/components/ConditionalNavbar.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\CoreWebVitals.jsx":{"id":"(app-pages-browser)/./src/components/CoreWebVitals.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Footer\\index.jsx":{"id":"(app-pages-browser)/./src/components/Footer/index.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\OptimizedBreadcrumbs.jsx":{"id":"(app-pages-browser)/./src/components/OptimizedBreadcrumbs.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\QuickCTA.jsx":{"id":"(app-pages-browser)/./src/components/QuickCTA.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\ContactStructuredData.jsx":{"id":"(app-pages-browser)/./src/components/SEO/ContactStructuredData.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\EnhancedStructuredData.jsx":{"id":"(app-pages-browser)/./src/components/SEO/EnhancedStructuredData.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\SmoothScrollProvider.jsx":{"id":"(app-pages-browser)/./src/components/ui/SmoothScrollProvider.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\styles\\microinteractions.css":{"id":"(app-pages-browser)/./src/styles/microinteractions.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\styles\\typography.css":{"id":"(app-pages-browser)/./src/styles/typography.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\styles\\advanced-grid.css":{"id":"(app-pages-browser)/./src/styles/advanced-grid.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\styles\\touch-targets.css":{"id":"(app-pages-browser)/./src/styles/touch-targets.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\styles\\advanced-typography.css":{"id":"(app-pages-browser)/./src/styles/advanced-typography.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\styles\\premium-utilities.css":{"id":"(app-pages-browser)/./src/styles/premium-utilities.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\styles\\navbar-fix.css":{"id":"(app-pages-browser)/./src/styles/navbar-fix.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\styles\\navbar-critical-fix.css":{"id":"(app-pages-browser)/./src/styles/navbar-critical-fix.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\error.jsx":{"id":"(app-pages-browser)/./src/app/error.jsx","name":"*","chunks":["app/error","static/chunks/app/error.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\not-found-bali.jsx":{"id":"(app-pages-browser)/./src/app/not-found-bali.jsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\page.jsx":{"id":"(app-pages-browser)/./src/app/page.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\esm\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\esm\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\esm\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\BlogPageClientContent.jsx":{"id":"(app-pages-browser)/./src/app/blog/BlogPageClientContent.jsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\UnifiedTypography.jsx":{"id":"(app-pages-browser)/./src/components/ui/UnifiedTypography.jsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\":[],"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\error":[],"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\not-found":[],"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":{"*":{"id":"(rsc)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js":{"*":{"id":"(rsc)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Analytics/LazyAnalytics.jsx":{"*":{"id":"(rsc)/./src/components/Analytics/LazyAnalytics.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ClientOnlyResponsiveChecker.jsx":{"*":{"id":"(rsc)/./src/components/ClientOnlyResponsiveChecker.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ConditionalNavbar.jsx":{"*":{"id":"(rsc)/./src/components/ConditionalNavbar.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/CoreWebVitals.jsx":{"*":{"id":"(rsc)/./src/components/CoreWebVitals.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Footer/index.jsx":{"*":{"id":"(rsc)/./src/components/Footer/index.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/OptimizedBreadcrumbs.jsx":{"*":{"id":"(rsc)/./src/components/OptimizedBreadcrumbs.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/QuickCTA.jsx":{"*":{"id":"(rsc)/./src/components/QuickCTA.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/SEO/ContactStructuredData.jsx":{"*":{"id":"(rsc)/./src/components/SEO/ContactStructuredData.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/SEO/EnhancedStructuredData.jsx":{"*":{"id":"(rsc)/./src/components/SEO/EnhancedStructuredData.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/SmoothScrollProvider.jsx":{"*":{"id":"(rsc)/./src/components/ui/SmoothScrollProvider.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/styles/microinteractions.css":{"*":{"id":"(rsc)/./src/styles/microinteractions.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/styles/typography.css":{"*":{"id":"(rsc)/./src/styles/typography.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/styles/advanced-grid.css":{"*":{"id":"(rsc)/./src/styles/advanced-grid.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/styles/touch-targets.css":{"*":{"id":"(rsc)/./src/styles/touch-targets.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/styles/advanced-typography.css":{"*":{"id":"(rsc)/./src/styles/advanced-typography.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/styles/premium-utilities.css":{"*":{"id":"(rsc)/./src/styles/premium-utilities.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/styles/navbar-fix.css":{"*":{"id":"(rsc)/./src/styles/navbar-fix.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/styles/navbar-critical-fix.css":{"*":{"id":"(rsc)/./src/styles/navbar-critical-fix.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/error.jsx":{"*":{"id":"(rsc)/./src/app/error.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found-bali.jsx":{"*":{"id":"(rsc)/./src/app/not-found-bali.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.jsx":{"*":{"id":"(rsc)/./src/app/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/blog/BlogPageClientContent.jsx":{"*":{"id":"(rsc)/./src/app/blog/BlogPageClientContent.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/UnifiedTypography.jsx":{"*":{"id":"(rsc)/./src/components/ui/UnifiedTypography.jsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}