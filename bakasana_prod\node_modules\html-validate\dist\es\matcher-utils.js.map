{"version": 3, "file": "matcher-utils.js", "sources": ["../../src/jest/utils/is-thenable.ts", "../../src/jest/utils/diverge.ts", "../../src/jest/utils/flatten-messages.ts", "../../src/jest/worker/create-sync-fn.ts"], "sourcesContent": ["/**\n * @internal\n */\nexport function isThenable<T>(value: T | Promise<T>): value is Promise<T> {\n\treturn value && typeof value === \"object\" && \"then\" in value && typeof value.then === \"function\";\n}\n", "import { isThenable } from \"./is-thenable\";\nimport { type Matcher<PERSON>ontext } from \"./matcher-context\";\nimport { type <PERSON><PERSON><PERSON><PERSON><PERSON> } from \"./matcher-result\";\n\ntype SyncCallback<T, TArgs extends any[]> = (\n\tthis: MatcherContext,\n\tactual: T,\n\t...args: TArgs\n) => MatcherResult;\n\n/**\n * @internal\n */\nexport interface MaybeAsyncCallback<TActual, TArgs extends any[]> {\n\t(this: MatcherContext, actual: TActual, ...args: TArgs): MatcherResult;\n\t(this: MatcherContext, actual: Promise<TActual>, ...args: TArgs): Promise<MatcherResult>;\n}\n\n/**\n * Creates a wrapped function based on the passed function.\n *\n * The returned function takes either a `T` or `Promise<T>`. If `T` the result\n * will be synchronous or if `Promise<T>` the result will be asynchronous.\n *\n * In practice this means that if you pass a synchronous object into it you will\n * maintain synchronous code but if you pass an asynchronous object you must\n * await the result.\n *\n * @internal\n */\nexport function diverge<T, TArgs extends any[]>(\n\tfn: SyncCallback<T, TArgs>,\n): MaybeAsyncCallback<T, TArgs> {\n\tfunction diverged(this: MatcherContext, actual: T, ...args: TArgs): MatcherResult;\n\tfunction diverged(\n\t\tthis: MatcherContext,\n\t\tactual: Promise<T>,\n\t\t...args: TArgs\n\t): Promise<MatcherResult>;\n\tfunction diverged(\n\t\tthis: MatcherContext,\n\t\tactual: T | Promise<T>,\n\t\t...args: TArgs\n\t): MatcherResult | Promise<MatcherResult> {\n\t\tif (isThenable(actual)) {\n\t\t\treturn actual.then((resolved) => fn.call(this, resolved, ...args));\n\t\t} else {\n\t\t\treturn fn.call(this, actual, ...args);\n\t\t}\n\t}\n\treturn diverged;\n}\n", "import { type Message } from \"../../message\";\nimport { type Report, type Result } from \"../../reporter\";\n\n/**\n * Takes all messages from all files and flattens to a single array.\n */\nexport function flattenMessages(report: Report): Message[] {\n\treturn report.results.reduce((aggregated: Message[], result: Result) => {\n\t\treturn aggregated.concat(result.messages);\n\t}, []);\n}\n", "import {\n\ttype MessagePort,\n\tMessageChannel,\n\tWorker,\n\treceiveMessageOnPort,\n} from \"node:worker_threads\";\nimport { legacyRequire } from \"../../resolve\";\nimport {\n\ttype AnyAsyncFn,\n\ttype AnyFn,\n\ttype MainToWorkerCommandMessage,\n\ttype MainToWorkerMessage,\n\ttype Syncify,\n\ttype WorkerToMainError,\n\ttype WorkerToMainMessage,\n} from \"./types\";\n\n/**\n * This is all based on the synckit library but without all the extra stuff such\n * as typescript, esbuld, pnp etc.\n */\n\nconst INT32_BYTES = 4;\nconst syncFnCache = new Map<string, AnyFn>();\nconst sharedBuffer = new SharedArrayBuffer(INT32_BYTES);\nconst sharedBufferView = new Int32Array(sharedBuffer, 0, 1);\n\nfunction isWorkerError<T>(value: WorkerToMainMessage<T>): value is WorkerToMainError {\n\treturn \"error\" in value;\n}\n\nfunction receiveMessageWithId<R>(port: MessagePort, expectedId: number): WorkerToMainMessage<R> {\n\tconst timeout = 30000;\n\tconst status = Atomics.wait(sharedBufferView, 0, 0, timeout);\n\tAtomics.store(sharedBufferView, 0, 0);\n\n\tif (![\"ok\", \"not-equal\"].includes(status)) {\n\t\tconst abortMsg: MainToWorkerCommandMessage = {\n\t\t\tid: expectedId,\n\t\t\tcmd: \"abort\",\n\t\t};\n\t\tport.postMessage(abortMsg);\n\t\tthrow new Error(`Internal error: Atomics.wait() failed: ${status}`);\n\t}\n\n\tconst reply = receiveMessageOnPort(port) as { message: WorkerToMainMessage<R> };\n\tconst { id, ...message } = reply.message;\n\n\tif (id < expectedId) {\n\t\treturn receiveMessageWithId(port, expectedId);\n\t}\n\n\tif (expectedId !== id) {\n\t\tthrow new Error(`Internal error: Expected id ${String(expectedId)} but got id ${String(id)}`);\n\t}\n\n\treturn { id, ...message };\n}\n\nfunction startWorkerThread<R, T extends AnyAsyncFn<R>>(\n\tworkerPath: string,\n): (...args: Parameters<T>) => R {\n\tconst { port1: mainPort, port2: workerPort } = new MessageChannel();\n\tconst workerPathUrl = legacyRequire.resolve(workerPath);\n\tconst worker = new Worker(workerPathUrl, {\n\t\teval: false,\n\t\tworkerData: { sharedBuffer, workerPort },\n\t\ttransferList: [workerPort],\n\t});\n\n\tlet nextID = 0;\n\n\tconst syncFn = (...args: Parameters<T>): R => {\n\t\tconst id = nextID++;\n\t\tconst msg: MainToWorkerMessage<Parameters<T>> = { id, args };\n\n\t\tworker.postMessage(msg);\n\n\t\tconst reply = receiveMessageWithId<R>(mainPort, id);\n\n\t\tif (isWorkerError(reply)) {\n\t\t\tthrow new Error(reply.error);\n\t\t}\n\n\t\treturn reply.result;\n\t};\n\n\tworker.unref();\n\n\treturn syncFn;\n}\n\nexport function createSyncFn<T extends AnyAsyncFn<R>, R = unknown>(workerPath: string): Syncify<T> {\n\tconst cachedSyncFn = syncFnCache.get(workerPath);\n\tif (cachedSyncFn) {\n\t\treturn cachedSyncFn as Syncify<T>;\n\t}\n\n\tconst syncFn = startWorkerThread<R, T>(workerPath);\n\tsyncFnCache.set(workerPath, syncFn);\n\treturn syncFn as Syncify<T>;\n}\n"], "names": [], "mappings": ";;;AAGO,SAAS,WAAc,KAAA,EAA4C;AACzE,EAAA,OAAO,KAAA,IAAS,OAAO,KAAA,KAAU,QAAA,IAAY,UAAU,KAAA,IAAS,OAAO,MAAM,IAAA,KAAS,UAAA;AACvF;;ACyBO,SAAS,QACf,EAAA,EAC+B;AAO/B,EAAA,SAAS,QAAA,CAER,WACG,IAAA,EACsC;AACzC,IAAA,IAAI,UAAA,CAAW,MAAM,CAAA,EAAG;AACvB,MAAA,OAAO,MAAA,CAAO,IAAA,CAAK,CAAC,QAAA,KAAa,EAAA,CAAG,KAAK,IAAA,EAAM,QAAA,EAAU,GAAG,IAAI,CAAC,CAAA;AAAA,KAClE,MAAO;AACN,MAAA,OAAO,EAAA,CAAG,IAAA,CAAK,IAAA,EAAM,MAAA,EAAQ,GAAG,IAAI,CAAA;AAAA;AACrC;AAED,EAAA,OAAO,QAAA;AACR;;AC7CO,SAAS,gBAAgB,MAAA,EAA2B;AAC1D,EAAA,OAAO,MAAA,CAAO,OAAA,CAAQ,MAAA,CAAO,CAAC,YAAuB,MAAA,KAAmB;AACvE,IAAA,OAAO,UAAA,CAAW,MAAA,CAAO,MAAA,CAAO,QAAQ,CAAA;AAAA,GACzC,EAAG,EAAE,CAAA;AACN;;ACYA,MAAM,WAAA,GAAc,CAAA;AACpB,MAAM,WAAA,uBAAkB,GAAA,EAAmB;AAC3C,MAAM,YAAA,GAAe,IAAI,iBAAA,CAAkB,WAAW,CAAA;AACtD,MAAM,gBAAA,GAAmB,IAAI,UAAA,CAAW,YAAA,EAAc,GAAG,CAAC,CAAA;AAE1D,SAAS,cAAiB,KAAA,EAA2D;AACpF,EAAA,OAAO,OAAA,IAAW,KAAA;AACnB;AAEA,SAAS,oBAAA,CAAwB,MAAmB,UAAA,EAA4C;AAC/F,EAAA,MAAM,OAAA,GAAU,GAAA;AAChB,EAAA,MAAM,SAAS,OAAA,CAAQ,IAAA,CAAK,gBAAA,EAAkB,CAAA,EAAG,GAAG,OAAO,CAAA;AAC3D,EAAA,OAAA,CAAQ,KAAA,CAAM,gBAAA,EAAkB,CAAA,EAAG,CAAC,CAAA;AAEpC,EAAA,IAAI,CAAC,CAAC,IAAA,EAAM,WAAW,CAAA,CAAE,QAAA,CAAS,MAAM,CAAA,EAAG;AAC1C,IAAA,MAAM,QAAA,GAAuC;AAAA,MAC5C,EAAA,EAAI,UAAA;AAAA,MACJ,GAAA,EAAK;AAAA,KACN;AACA,IAAA,IAAA,CAAK,YAAY,QAAQ,CAAA;AACzB,IAAA,MAAM,IAAI,KAAA,CAAM,CAAA,uCAAA,EAA0C,MAAM,CAAA,CAAE,CAAA;AAAA;AAGnE,EAAA,MAAM,KAAA,GAAQ,qBAAqB,IAAI,CAAA;AACvC,EAAA,MAAM,EAAE,EAAA,EAAI,GAAG,OAAA,KAAY,KAAA,CAAM,OAAA;AAEjC,EAAA,IAAI,KAAK,UAAA,EAAY;AACpB,IAAA,OAAO,oBAAA,CAAqB,MAAM,UAAU,CAAA;AAAA;AAG7C,EAAA,IAAI,eAAe,EAAA,EAAI;AACtB,IAAA,MAAM,IAAI,KAAA,CAAM,CAAA,4BAAA,EAA+B,MAAA,CAAO,UAAU,CAAC,CAAA,YAAA,EAAe,MAAA,CAAO,EAAE,CAAC,CAAA,CAAE,CAAA;AAAA;AAG7F,EAAA,OAAO,EAAE,EAAA,EAAI,GAAG,OAAA,EAAQ;AACzB;AAEA,SAAS,kBACR,UAAA,EACgC;AAChC,EAAA,MAAM,EAAE,KAAA,EAAO,QAAA,EAAU,OAAO,UAAA,EAAW,GAAI,IAAI,cAAA,EAAe;AAClE,EAAA,MAAM,aAAA,GAAgB,aAAA,CAAc,OAAA,CAAQ,UAAU,CAAA;AACtD,EAAA,MAAM,MAAA,GAAS,IAAI,MAAA,CAAO,aAAA,EAAe;AAAA,IACxC,IAAA,EAAM,KAAA;AAAA,IACN,UAAA,EAAY,EAAE,YAAA,EAAc,UAAA,EAAW;AAAA,IACvC,YAAA,EAAc,CAAC,UAAU;AAAA,GACzB,CAAA;AAED,EAAA,IAAI,MAAA,GAAS,CAAA;AAEb,EAAA,MAAM,MAAA,GAAS,IAAI,IAAA,KAA2B;AAC7C,IAAA,MAAM,EAAA,GAAK,MAAA,EAAA;AACX,IAAA,MAAM,GAAA,GAA0C,EAAE,EAAA,EAAI,IAAA,EAAK;AAE3D,IAAA,MAAA,CAAO,YAAY,GAAG,CAAA;AAEtB,IAAA,MAAM,KAAA,GAAQ,oBAAA,CAAwB,QAAA,EAAU,EAAE,CAAA;AAElD,IAAA,IAAI,aAAA,CAAc,KAAK,CAAA,EAAG;AACzB,MAAA,MAAM,IAAI,KAAA,CAAM,KAAA,CAAM,KAAK,CAAA;AAAA;AAG5B,IAAA,OAAO,KAAA,CAAM,MAAA;AAAA,GACd;AAEA,EAAA,MAAA,CAAO,KAAA,EAAM;AAEb,EAAA,OAAO,MAAA;AACR;AAEO,SAAS,aAAmD,UAAA,EAAgC;AAClG,EAAA,MAAM,YAAA,GAAe,WAAA,CAAY,GAAA,CAAI,UAAU,CAAA;AAC/C,EAAA,IAAI,YAAA,EAAc;AACjB,IAAA,OAAO,YAAA;AAAA;AAGR,EAAA,MAAM,MAAA,GAAS,kBAAwB,UAAU,CAAA;AACjD,EAAA,WAAA,CAAY,GAAA,CAAI,YAAY,MAAM,CAAA;AAClC,EAAA,OAAO,MAAA;AACR;;;;"}