# BAKASANA - Environment Variables Template
# Copy this file to .env.local and fill in your values

# SEO Verification Codes
GOOGLE_SITE_VERIFICATION=your_google_verification_code
YANDEX_VERIFICATION=your_yandex_verification_code
BING_VERIFICATION=your_bing_verification_code
PINTEREST_VERIFICATION=your_pinterest_verification_code

# Admin Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Analytics & Monitoring
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
NEXT_PUBLIC_MIXPANEL_TOKEN=your_mixpanel_token
NEXT_PUBLIC_HOTJAR_ID=your_hotjar_site_id
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id
NEXT_PUBLIC_FB_PIXEL_ID=your_facebook_pixel_id

# Performance Monitoring APIs (Server-side only)
GTMETRIX_API_KEY=your_gtmetrix_api_key
PINGDOM_API_KEY=your_pingdom_api_key
UPTIMEROBOT_API_KEY=your_uptimerobot_api_key

# Email/Newsletter (Optional)
CONVERTKIT_API_KEY=your_convertkit_api_key
CONVERTKIT_FORM_ID=your_convertkit_form_id

# Maps (Optional)
NEXT_PUBLIC_MAPBOX_TOKEN=your_mapbox_token

# Sanity CMS (Optional)
NEXT_PUBLIC_SANITY_PROJECT_ID=your_sanity_project_id
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=your_sanity_api_token