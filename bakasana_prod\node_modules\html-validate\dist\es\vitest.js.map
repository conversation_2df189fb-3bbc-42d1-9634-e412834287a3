{"version": 3, "file": "vitest.js", "sources": ["../../src/vitest/vitest.ts"], "sourcesContent": ["import \"./augmentation\";\n\nimport { expect } from \"vitest\";\nimport {\n\ttoBeValid,\n\ttoBeInvalid,\n\ttoHTMLValidate,\n\ttoHaveError,\n\ttoHaveErrors,\n} from \"../jest/matchers\";\n\nexpect.extend({\n\ttoBeValid: toBeValid(),\n\ttoBeInvalid: toBeInvalid(),\n\ttoHTMLValidate: toHTMLValidate(expect, undefined),\n\ttoHaveError: toHaveError(expect, undefined),\n\ttoHaveErrors: toHaveErrors(expect, undefined),\n});\n"], "names": ["toBeValid", "toBeInvalid", "toHTMLValidate", "toHaveError", "toHaveErrors"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAWA,MAAA,CAAO,MAAA,CAAO;AAAA,EACb,WAAWA,eAAA,EAAU;AAAA,EACrB,aAAaC,eAAA,EAAY;AAAA,EACzB,cAAA,EAAgBC,eAAA,CAAe,MAAA,EAAQ,MAAS,CAAA;AAAA,EAChD,WAAA,EAAaC,eAAA,CAAY,MAAA,EAAQ,MAAS,CAAA;AAAA,EAC1C,YAAA,EAAcC,aAAA,CAAa,MAAA,EAAQ,MAAS;AAC7C,CAAC,CAAA"}