:root{--text-xs:clamp(.75rem,.7rem+.25vw,.875rem);--text-sm:clamp(.875rem,.8rem+.375vw,1rem);--text-base:clamp(1rem,.9rem+.5vw,1.125rem);--text-lg:clamp(1.125rem,1rem+.625vw,1.375rem);--text-xl:clamp(1.25rem,1.1rem+.75vw,1.625rem);--text-2xl:clamp(1.5rem,1.3rem+1vw,2rem);--text-3xl:clamp(1.875rem,1.6rem+1.375vw,2.5rem);--text-4xl:clamp(2.25rem,1.9rem+1.75vw,3.25rem);--text-5xl:clamp(3rem,2.5rem+2.5vw,4.5rem);--text-6xl:clamp(4rem,3rem+5vw,7rem);--text-7xl:clamp(5rem,4rem+8vw,10rem);--leading-none:1;--leading-tight:1.1;--leading-snug:1.25;--leading-normal:1.5;--leading-relaxed:1.625;--leading-loose:2;--tracking-tighter:clamp(-.05em,-.02vw,-.025em);--tracking-tight:clamp(-.025em,-.01vw,-.0125em);--tracking-normal:0;--tracking-wide:clamp(.025em,.01vw,.05em);--tracking-wider:clamp(.05em,.02vw,.1em);--tracking-widest:clamp(.1em,.05vw,.25em)}@media (max-width:480px){:root{--text-6xl:clamp(2.5rem,10vw,4rem);--text-5xl:clamp(2rem,8vw,3rem);--text-4xl:clamp(1.75rem,6vw,2.5rem);--text-3xl:clamp(1.5rem,5vw,2rem)}}@media (min-width:481px) and (max-width:1024px){:root{--text-6xl:clamp(3rem,8vw,5rem);--text-5xl:clamp(2.5rem,6vw,3.5rem);--text-4xl:clamp(2rem,5vw,2.75rem)}}@media (min-width:1440px){:root{--text-7xl:clamp(6rem,8vw,12rem);--text-6xl:clamp(5rem,6vw,8rem);--text-5xl:clamp(3.5rem,4vw,5rem)}}@media (prefers-reduced-motion:reduce){*{transition:none !important;animation:none !important}}@media print{:root{--text-7xl:48pt;--text-6xl:36pt;--text-5xl:24pt;--text-4xl:18pt;--text-3xl:14pt;--text-2xl:12pt;--text-xl:11pt;--text-base:10pt;--text-sm:9pt;--text-xs:8pt}}