import { Suspense } from 'react';
import React from 'react';

import FormPreconnect from '@/components/FormPreconnect';
import PerformantWhatsApp from '@/components/PerformantWhatsApp';

import ContactForm from './ContactForm';


export const metadata = {
  title: 'Kontakt - BAKASANA',
  description: 'Skontaktuj się z nami w sprawie retreatów jogi na Bali i Sri Lanka. Julia Jakubowicz - certyfikowana instruktorka jogi.',
  openGraph: {
    title: 'Kontakt - BAKASANA',
    description: 'Skontaktuj się z nami w sprawie retreatów jogi na Bali i Sri Lanka. <PERSON>icz - certyfikowana instruktorka jogi.',
    images: ['/images/og-image.jpg'],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Kontakt - BAKASANA',
    description: 'Skontaktuj się z nami w sprawie retreatów jogi na Bali i Sri Lanka. <PERSON> - certyfikowana instruktorka jogi.',
  }
};

export default function KontaktPage() {
  return (
    <>
      <FormPreconnect />
      <div className='bg-sanctuary min-h-screen'>
        {/* HERO SECTION - Magazine Style Header */}
        <section className='magazine-hero'>
          <div className='magazine-hero-content'>
            <div className='magazine-header-line'></div>

            <h1 className='magazine-title'>Kontakt</h1>

            <p className='magazine-subtitle'>Zacznij swoją podróż do siebie</p>

            <div className='magazine-meta'>
              Bali • Sri Lanka • Duchowa Transformacja
            </div>

            <div className='magazine-header-line'></div>
          </div>
        </section>

        {/* INSPIRATIONAL SECTION */}
        <section className='container'>
          <div className='text-center mb-20'>
            <div className='section-divider mb-xl'></div>
            <h2 className='section-header mb-lg'>
              Porozmawiajmy o Twojej podróży
            </h2>
            <p className='body-text max-w-3xl mx-auto mb-lg opacity-80'>
              Każda transformacja zaczyna się od pierwszego kroku. Napisz do
              mnie, a wspólnie znajdziemy idealny retreat, który otworzy przed
              Tobą drzwi do wewnętrznego spokoju i odkrycia prawdziwej siebie.
            </p>

            {/* Sacred Quote */}
            <div className='flex items-center justify-center my-12'>
              <div className='flex items-center gap-sm text-charcoal-gold/60'>
                <div className='w-12 h-px bg-charcoal-gold/30'></div>
                <span className='text-xl opacity-60 /* TODO: Replace with CardTitle */'>
                  ॐ
                </span>
                <div className='w-12 h-px bg-charcoal-gold/30'></div>
              </div>
            </div>

            <p className='body-text italic text-charcoal-gold/80 mb-lg'>
              "Najdłuższa podróż zaczyna się od jednego kroku"
            </p>
          </div>

          {/* CONTACT OPTIONS */}
          <div className='grid grid-cols-1 md:grid-cols-3 gap-xl mb-20 max-w-4xl mx-auto'>
            <div className='text-center p-8'>
              <h3 className='font-light text-charcoal mb-sm tracking-wide'>
                WhatsApp
              </h3>
              <p className='text-sm text-stone font-light mb-sm'>
                Szybki kontakt
              </p>
              <div className='flex justify-center'>
                <PerformantWhatsApp
                  size='md'
                  variant='button'
                  className='px-hero-padding py-3 text-center'
                />
              </div>
            </div>

            <div className='text-center p-8'>
              <h3 className='font-light text-charcoal mb-sm tracking-wide'>
                Instagram
              </h3>
              <p className='text-sm text-stone font-light mb-sm'>
                Codzienne inspiracje
              </p>
              <a
                href='https://www.instagram.com/fly_with_bakasana'
                target='_blank'
                rel='noopener noreferrer'
                className='btn-ghost'
              >
                Obserwuj
              </a>
            </div>

            <div className='text-center p-8'>
              <h3 className='font-light text-charcoal mb-sm tracking-wide'>
                Email
              </h3>
              <p className='text-sm text-stone font-light mb-sm'>
                Szczegółowe zapytania
              </p>
              <a href='mailto:<EMAIL>' className='btn-ghost'>
                Napisz email
              </a>
            </div>
          </div>

          {/* CONTACT FORM SECTION */}
          <div id='contact-form' className='max-w-4xl mx-auto'>
            <div className='text-center mb-2xl'>
              <h3 className='section-header mb-lg'>Formularz kontaktowy</h3>
              <p className='body-text opacity-80'>
                Wypełnij formularz, a odpowiem w ciągu 24 godzin
              </p>
            </div>

            <Suspense
              fallback={
                <div className='flex items-center justify-center h-[40vh] text-stone'>
                  Ładowanie...
                </div>
              }
            >
              <ContactForm />
            </Suspense>
          </div>
        </section>

        {/* Floating WhatsApp */}
        <div className='fixed bottom-6 right-6 z-50'>
          <PerformantWhatsApp
            size='lg'
            message='Cześć Julia! Chciałabym/chciałbym porozmawiać o retreatach. Czy możemy się skontaktować?'
          />
        </div>
      </div>
    </>
  );
}
