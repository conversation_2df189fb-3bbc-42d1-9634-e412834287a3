'use client';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';

import {

export const metadata = {
  title: 'Bookings - BAKASANA',
  description: 'Strona bookings - BAKASANA retreaty jogi na Bali i Sri Lanka z Julią Jakubowicz.',
  openGraph: {
    title: 'Bookings - BAKASANA',
    description: 'Strona bookings - BAKASANA retreaty jogi na Bali i Sri Lanka z Julią Jakubowicz.',
    images: ['/images/og-image.jpg'],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Bookings - BAKASANA',
    description: 'Strona bookings - BAKASANA retreaty jogi na Bali i Sri Lanka z Julią Jakubowicz.',
  }
};

  HeroTitle,
  SectionTitle,
  CardTitle,
  BodyText,
} from '@/components/ui/UnifiedTypography';
import UnifiedButton from '@/components/ui/UnifiedButton';

export default function AdminBookingsPage() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const router = useRouter();

  useEffect(() => {
    // Sprawdź autentykację
    const token = localStorage.getItem('admin-token');
    if (!token) {
      router.push('/admin');
      return;
    }

    verifyTokenAndLoadData(token);
  }, [router]);

  const verifyTokenAndLoadData = async token => {
    try {
      const response = await fetch('/api/admin/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        setIsAuthenticated(true);
        await loadBookings();
      } else {
        localStorage.removeItem('admin-token');
        router.push('/admin');
      }
    } catch (error) {
      console.error('Auth verification failed:', error);
      router.push('/admin');
    }
    setLoading(false);
  };

  const loadBookings = async () => {
    try {
      const response = await fetch('/api/admin/bookings');
      if (response.ok) {
        const data = await response.json();
        setBookings(data.bookings || []);
      }
    } catch (error) {
      console.error('Failed to load bookings:', error);
    }
  };

  const updateBookingStatus = async (bookingId, newStatus) => {
    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/bookings/${bookingId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        await loadBookings(); // Odśwież listę
      }
    } catch (error) {
      console.error('Failed to update booking:', error);
    }
  };

  const getStatusColor = status => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = status => {
    switch (status) {
      case 'pending':
        return 'Oczekuje';
      case 'confirmed':
        return 'Potwierdzona';
      case 'cancelled':
        return 'Anulowana';
      default:
        return 'Nieznany';
    }
  };

  const filteredBookings = bookings.filter(booking => {
    if (filter === 'all') return true;
    return booking.status === filter;
  });

  if (loading) {
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin h-12 w-12 border-b-2 border-charcoal mx-auto mb-sm'></div>
          <p className='text-charcoal'>Ładowanie...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className='min-h-screen bg-gradient-to-br from-sanctuary to-whisper'>
      {/* Header */}
      <header className='bg-white shadow-sm border-b border-charcoal/10'>
        <div className='max-w-7xl mx-auto px-container-sm sm:px-hero-padding lg:px-hero-padding'>
          <div className='flex justify-between items-center h-16'>
            <div className='flex items-center'>
              <button
                onClick={() => router.push('/admin')}
                className='text-charcoal hover:text-charcoal/70 mr-4'
              >
                ← Powrót
              </button>
              <h1 className='text-xl font-cormorant text-charcoal /* TODO: Replace with CardTitle */'>
                Zarządzanie Rezerwacjami
              </h1>
            </div>
            <div className='flex items-center space-x-4'>
              <span className='text-sm text-charcoal-light'>
                {filteredBookings.length} rezerwacji
              </span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className='max-w-7xl mx-auto py-8 px-container-sm sm:px-hero-padding lg:px-hero-padding'>
        {/* Filters */}
        <div className='bg-white shadow-soft p-6 mb-md'>
          <CardTitle level={3}>Filtry</CardTitle>
          <div className='flex flex-wrap gap-2'>
            {[
              { key: 'all', label: 'Wszystkie', count: bookings.length },
              {
                key: 'pending',
                label: 'Oczekujące',
                count: bookings.filter(b => b.status === 'pending').length,
              },
              {
                key: 'confirmed',
                label: 'Potwierdzone',
                count: bookings.filter(b => b.status === 'confirmed').length,
              },
              {
                key: 'cancelled',
                label: 'Anulowane',
                count: bookings.filter(b => b.status === 'cancelled').length,
              },
            ].map(filterOption => (
              <button
                key={filterOption.key}
                onClick={() => setFilter(filterOption.key)}
                className={`px-container-sm py-2 text-sm font-medium transition-colors ${
                  filter === filterOption.key
                    ? 'bg-charcoal text-white'
                    : 'bg-charcoal/10 text-charcoal hover:bg-charcoal/20'
                }`}
              >
                {filterOption.label} ({filterOption.count})
              </button>
            ))}
          </div>
        </div>

        {/* Bookings List */}
        <div className='bg-white shadow-soft overflow-hidden'>
          {filteredBookings.length === 0 ? (
            <div className='p-8 text-center'>
              <div className='text-6xl mb-sm /* TODO: Replace with HeroTitle */'>
                📅
              </div>
              <CardTitle level={3}>Brak rezerwacji</CardTitle>
              <p className='text-charcoal-light'>
                {filter === 'all'
                  ? 'Nie ma jeszcze żadnych rezerwacji.'
                  : `Nie ma rezerwacji o statusie "${getStatusText(filter)}".`}
              </p>
            </div>
          ) : (
            <div className='overflow-x-auto'>
              <table className='min-w-full divide-y divide-temple/10'>
                <thead className='bg-charcoal/5'>
                  <tr>
                    <th className='px-hero-padding py-3 text-left text-xs font-medium text-charcoal uppercase tracking-wider'>
                      Klient
                    </th>
                    <th className='px-hero-padding py-3 text-left text-xs font-medium text-charcoal uppercase tracking-wider'>
                      Program
                    </th>
                    <th className='px-hero-padding py-3 text-left text-xs font-medium text-charcoal uppercase tracking-wider'>
                      Data
                    </th>
                    <th className='px-hero-padding py-3 text-left text-xs font-medium text-charcoal uppercase tracking-wider'>
                      Status
                    </th>
                    <th className='px-hero-padding py-3 text-left text-xs font-medium text-charcoal uppercase tracking-wider'>
                      Akcje
                    </th>
                  </tr>
                </thead>
                <tbody className='bg-white divide-y divide-temple/10'>
                  {filteredBookings.map(booking => (
                    <tr key={booking.id} className='hover:bg-charcoal/5'>
                      <td className='px-hero-padding py-4 whitespace-nowrap'>
                        <div>
                          <div className='text-sm font-medium text-charcoal'>
                            {booking.firstName} {booking.lastName}
                          </div>
                          <div className='text-sm text-charcoal-light'>
                            {booking.email}
                          </div>
                          <div className='text-sm text-charcoal-light'>
                            {booking.phone}
                          </div>
                        </div>
                      </td>
                      <td className='px-hero-padding py-4 whitespace-nowrap'>
                        <div className='text-sm text-charcoal'>
                          {booking.program}
                        </div>
                        <div className='text-sm text-charcoal-light'>
                          {booking.destination}
                        </div>
                      </td>
                      <td className='px-hero-padding py-4 whitespace-nowrap'>
                        <div className='text-sm text-charcoal'>
                          {new Date(booking.createdAt).toLocaleDateString(
                            'pl-PL'
                          )}
                        </div>
                        <div className='text-sm text-charcoal-light'>
                          {new Date(booking.createdAt).toLocaleTimeString(
                            'pl-PL'
                          )}
                        </div>
                      </td>
                      <td className='px-hero-padding py-4 whitespace-nowrap'>
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-semibold ${getStatusColor(booking.status)}`}
                        >
                          {getStatusText(booking.status)}
                        </span>
                      </td>
                      <td className='px-hero-padding py-4 whitespace-nowrap text-sm font-medium'>
                        <div className='flex space-x-2'>
                          {booking.status === 'pending' && (
                            <>
                              <button
                                onClick={() =>
                                  updateBookingStatus(booking.id, 'confirmed')
                                }
                                className='text-green-600 hover:text-green-900'
                              >
                                Potwierdź
                              </button>
                              <button
                                onClick={() =>
                                  updateBookingStatus(booking.id, 'cancelled')
                                }
                                className='text-red-600 hover:text-red-900'
                              >
                                Anuluj
                              </button>
                            </>
                          )}
                          {booking.status === 'confirmed' && (
                            <button
                              onClick={() =>
                                updateBookingStatus(booking.id, 'cancelled')
                              }
                              className='text-red-600 hover:text-red-900'
                            >
                              Anuluj
                            </button>
                          )}
                          {booking.status === 'cancelled' && (
                            <button
                              onClick={() =>
                                updateBookingStatus(booking.id, 'pending')
                              }
                              className='text-blue-600 hover:text-blue-900'
                            >
                              Przywróć
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
