{"timestamp": "2025-07-24T20:16:50.983Z", "summary": {"errors": 2, "seoIssues": 14, "accessibilityIssues": 99, "warnings": 303, "suggestions": 105}, "details": {"errors": ["layout.jsx: Missing viewport meta tag for responsive design", "layout.jsx: Missing lang attribute on html element"], "seoIssues": ["page.jsx: Missing meta description", "page.jsx: Missing Open Graph metadata for social sharing", "page.jsx: Missing meta description", "page.jsx: Missing Open Graph metadata for social sharing", "page.jsx: Missing meta description", "page.jsx: Missing Open Graph metadata for social sharing", "page.jsx: Missing Open Graph metadata for social sharing", "page.jsx: Missing Open Graph metadata for social sharing", "page.jsx: Missing Open Graph metadata for social sharing", "page.jsx: Missing page title metadata", "page.jsx: Missing Open Graph metadata for social sharing", "page.jsx: Missing Open Graph metadata for social sharing", "page.jsx: Missing Open Graph metadata for social sharing", "page.jsx: Missing Open Graph metadata for social sharing"], "accessibilityIssues": ["page.jsx: Consider adding ARIA attributes for better accessibility", "page.jsx: Interactive elements should be keyboard accessible", "page.jsx: Consider adding ARIA attributes for better accessibility", "page.jsx: Interactive elements should be keyboard accessible", "BlogPageClientContent.jsx: Consider adding ARIA attributes for better accessibility", "BlogPageClientContent.jsx: Interactive elements should be keyboard accessible", "page.jsx: Consider adding ARIA attributes for better accessibility", "error.jsx: Consider adding ARIA attributes for better accessibility", "error.jsx: Interactive elements should be keyboard accessible", "page.jsx: Consider adding ARIA attributes for better accessibility", "page.jsx: Consider adding ARIA attributes for better accessibility", "page.jsx: Consider adding ARIA attributes for better accessibility", "page.jsx: Interactive elements should be keyboard accessible", "page.jsx: Consider adding ARIA attributes for better accessibility", "page.jsx: Consider adding ARIA attributes for better accessibility", "page.jsx: Consider adding ARIA attributes for better accessibility", "page.jsx: Consider adding ARIA attributes for better accessibility", "page.jsx: Consider adding ARIA attributes for better accessibility", "page.jsx: Interactive elements should be keyboard accessible", "page.jsx: Consider adding ARIA attributes for better accessibility", "AccessibilityEnhancer.jsx: Interactive elements should be keyboard accessible", "AccessibilityProvider.jsx: Interactive elements should be keyboard accessible", "ConversionOptimization.jsx: Consider adding ARIA attributes for better accessibility", "TravelAnalytics.jsx: Consider adding ARIA attributes for better accessibility", "EnhancedBookingFlow.jsx: Consider adding ARIA attributes for better accessibility", "EnhancedBookingFlow.jsx: Interactive elements should be keyboard accessible", "BookingCalendar.jsx: Consider adding ARIA attributes for better accessibility", "BookingCalendar.jsx: Interactive elements should be keyboard accessible", "BookingForm.jsx: Consider adding ARIA attributes for better accessibility", "BookingForm.jsx: Interactive elements should be keyboard accessible", "ClientInteractiveButton.jsx: Interactive elements should be keyboard accessible", "ClientOnlyResponsiveChecker.jsx: Consider adding ARIA attributes for better accessibility", "ClientOnlyResponsiveChecker.jsx: Interactive elements should be keyboard accessible", "CookieConsent.jsx: Consider adding ARIA attributes for better accessibility", "CookieConsent.jsx: Interactive elements should be keyboard accessible", "CriticalCSS.jsx: Consider adding ARIA attributes for better accessibility", "AdvancedErrorBoundary.jsx: Consider adding ARIA attributes for better accessibility", "AdvancedErrorBoundary.jsx: Interactive elements should be keyboard accessible", "FAQSection.jsx: Consider adding ARIA attributes for better accessibility", "FAQSection.jsx: Interactive elements should be keyboard accessible", "FitsseyIntegration.jsx: Consider adding ARIA attributes for better accessibility", "FitsseyIntegration.jsx: Interactive elements should be keyboard accessible", "FitsseySchedule.jsx: Consider adding ARIA attributes for better accessibility", "GhostNavbar.jsx: Interactive elements should be keyboard accessible", "HeroVariantsDemo.jsx: Consider adding ARIA attributes for better accessibility", "HeroVariantsDemo.jsx: Interactive elements should be keyboard accessible", "InteractiveMap.jsx: Consider adding ARIA attributes for better accessibility", "InteractiveMap.jsx: Interactive elements should be keyboard accessible", "InvestmentSection.jsx: Interactive elements should be keyboard accessible", "LuxuryElements.jsx: Consider adding ARIA attributes for better accessibility", "LuxuryElements.jsx: Interactive elements should be keyboard accessible", "LuxuryScrollProgress.jsx: Consider adding ARIA attributes for better accessibility", "LuxuryScrollProgress.jsx: Interactive elements should be keyboard accessible", "LuxuryWhatsApp.jsx: Consider adding ARIA attributes for better accessibility", "LuxuryWhatsApp.jsx: Interactive elements should be keyboard accessible", "MinimalistHero.jsx: Consider adding ARIA attributes for better accessibility", "ClientNavbar.jsx: Interactive elements should be keyboard accessible", "NewsletterSignup.jsx: Consider adding ARIA attributes for better accessibility", "NewsletterSignup.jsx: Interactive elements should be keyboard accessible", "PerfectNavbar.jsx: Interactive elements should be keyboard accessible", "ImageOptimizer.jsx: Consider adding ARIA attributes for better accessibility", "ImageOptimizer.jsx: Interactive elements should be keyboard accessible", "PerformantWhatsApp.jsx: Interactive elements should be keyboard accessible", "PWAInstall.jsx: Consider adding ARIA attributes for better accessibility", "PWAInstall.jsx: Interactive elements should be keyboard accessible", "PWAInstaller.jsx: Consider adding ARIA attributes for better accessibility", "PWAInstaller.jsx: Interactive elements should be keyboard accessible", "ResponsiveChecker.jsx: Consider adding ARIA attributes for better accessibility", "ResponsiveChecker.jsx: Interactive elements should be keyboard accessible", "RetreatCalendar.jsx: Consider adding ARIA attributes for better accessibility", "RetreatCalendar.jsx: Interactive elements should be keyboard accessible", "ScrollReveal.jsx: Consider adding ARIA attributes for better accessibility", "EnterpriseMetaTags.jsx: Consider adding ARIA attributes for better accessibility", "TestimonialSlider.jsx: Interactive elements should be keyboard accessible", "Toast.jsx: Interactive elements should be keyboard accessible", "CurrencyConverter.jsx: Consider adding ARIA attributes for better accessibility", "CurrencyConverter.jsx: Interactive elements should be keyboard accessible", "InteractiveMap.jsx: Consider adding ARIA attributes for better accessibility", "InteractiveMap.jsx: Interactive elements should be keyboard accessible", "TravelGuide.jsx: Consider adding ARIA attributes for better accessibility", "TravelGuide.jsx: Interactive elements should be keyboard accessible", "button.jsx: Consider adding ARIA attributes for better accessibility", "EnhancedButton.jsx: Consider adding ARIA attributes for better accessibility", "EnhancedButton.jsx: Interactive elements should be keyboard accessible", "ErrorBoundary.jsx: Interactive elements should be keyboard accessible", "PageTransition.jsx: Consider adding ARIA attributes for better accessibility", "PageTransition.jsx: Interactive elements should be keyboard accessible", "RippleButton.jsx: Consider adding ARIA attributes for better accessibility", "RippleButton.jsx: Interactive elements should be keyboard accessible", "UnifiedButton.jsx: Consider adding ARIA attributes for better accessibility", "AccessibilityEnhancer.jsx: Interactive elements should be keyboard accessible", "KeyboardShortcuts.jsx: Consider adding ARIA attributes for better accessibility", "KeyboardShortcuts.jsx: Interactive elements should be keyboard accessible", "MagneticButton.jsx: Consider adding ARIA attributes for better accessibility", "MagneticButton.jsx: Interactive elements should be keyboard accessible", "MicroAnimations.jsx: Interactive elements should be keyboard accessible", "PerformanceMonitor.jsx: Consider adding ARIA attributes for better accessibility", "PerformanceMonitor.jsx: Interactive elements should be keyboard accessible", "useAdvancedAnimations.js: Consider adding ARIA attributes for better accessibility"], "warnings": ["page.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "route.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "route.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "route.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "route.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "route.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "route.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "route.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "route.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "route.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "route.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "route.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "route.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "route.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "route.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "BlogPageClientContent.jsx: Input 1 may be missing proper labeling", "page.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "page.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "metadata.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "metadata.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "page.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "page.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "page.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "ContactForm.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "layout.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "metadata.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "metadata.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "not-found-bali.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "not-found-bali.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "not-found.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "not-found.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "layout.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "layout.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "page.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "opengraph-image.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "opengraph-image.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "page.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "page.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "metadata.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "metadata.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "page.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "metadata.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "metadata.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "page.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "robots.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "robots.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "sitemap.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "sitemap.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "page.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "page.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "AccessibilityEnhancer.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "AccessibilityProvider.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ABTestingEngine.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ABTestingEngine.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "AdvancedAnalytics.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "AdvancedAnalytics.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "AnalyticsWrapper.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "AnalyticsWrapper.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "ConversionOptimization.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ConversionOptimization.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "EnterpriseAnalytics.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "EnterpriseAnalytics.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "GoogleAnalytics.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "GoogleAnalytics.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "index.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "index.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "LazyAnalytics.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "LazyAnalytics.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "SEOTracker.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "SEOTracker.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "SuperiorAnalytics.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "SuperiorAnalytics.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "TravelAnalytics.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "TravelAnalytics.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "AnimatedCounter.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "AnimatedCounter.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "BlogWhatsAppCTA.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "BlogWhatsAppCTA.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "EnhancedBookingFlow.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "BookingCalendar.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "BookingForm.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "Breadcrumbs.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "ClientInteractiveButton.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ClientInteractiveButton.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "ClientLayout.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "ClientOnly.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ClientOnly.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "ClientOnlyResponsiveChecker.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ConditionalNavbar.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ConditionalNavbar.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "CookieConsent.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "CookieConsent.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "CoreWebVitals.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "CoreWebVitals.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "CriticalCSS.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "CriticalCSS.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "AdvancedErrorBoundary.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "AdvancedErrorBoundary.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "ErrorBoundary.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ErrorBoundary.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "EventCard.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "FitsseyIntegration.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "FitsseyIntegration.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "FitsseySchedule.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "FitsseySchedule.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "ServerFooter.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "FormPreconnect.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "FormPreconnect.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "GhostNavbar.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "index.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "BakasanaHero.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "CustomColorHero.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "ElegantBakasanaHero.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "HeroVariantsDemo.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "MinimalistYogaHero.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "ProfessionalHero.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "SimpleHero.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "InteractiveMap.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "LuxuryElements.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "LuxuryElements.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "LuxuryScrollProgress.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "LuxuryScrollProgress.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "LuxuryTestimonials.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "LuxuryWhatsApp.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "MinimalistHero.jsx: Input 1 may be missing proper labeling", "MinimalistNavbar.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "ClientNavbar.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "NewsletterSignup.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "NewsletterSignup.jsx: Input 1 may be missing proper labeling", "NewsletterSignup.jsx: Input 2 may be missing proper labeling", "NewsletterSignup.jsx: Input 3 may be missing proper labeling", "OnlineClassesStyles.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "OnlineClassesStyles.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "OptimizedBreadcrumbs.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "OptimizedIcon.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "OptimizedIcon.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "OptimizedImage.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "OptimizedImage.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "PerfectNavbar.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "ImageOptimizer.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ImageOptimizer.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "LoadingStates.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "PerformanceMonitor.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "PerformanceMonitor.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "RealUserMonitoring.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "RealUserMonitoring.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "PerformanceHints.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "PerformanceHints.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "PerformantWhatsApp.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "PerformantWhatsApp.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "PWAInstall.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ServiceWorker.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ServiceWorker.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "PWAInstaller.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "QuickCTA.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "QuickCTA.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "ResponsiveChecker.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ResponsiveCheckerWrapper.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ResponsiveCheckerWrapper.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "RetreatCalendar.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "SanityTestimonials.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "ScrollReveal.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ScrollReveal.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "AdvancedSEO.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "AdvancedSEO.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "CanonicalURL.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "CanonicalURL.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "CompetitorAnalysis.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "CompetitorAnalysis.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "ContactStructuredData.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ContactStructuredData.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "EnhancedStructuredData.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "EnhancedStructuredData.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "EnterpriseMetaTags.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "EnterpriseMetaTags.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "LocalBusinessSchema.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "LocalBusinessSchema.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "MetaTags.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "MetaTags.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "SchemaOrg.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "SchemaOrg.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "TravelSEO.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "TravelSEO.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "SEOBreadcrumbs.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "SmartBreadcrumbs.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "TestimonialSlider.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "Toast.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "Toast.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "TransformationCTA.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "CurrencyConverter.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "InteractiveMap.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "TravelGuide.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "WeatherWidget.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "TrustBadges.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "badge.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "badge.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "BlogComponents.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "button.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "button.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "ElegantQuote.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "EnhancedButton.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "EnhancedButton.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "ErrorBoundary.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ErrorBoundary.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "GlassCard.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "IconSystem.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "IconSystem.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "input.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "input.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "input.jsx: Input 1 may be missing proper labeling", "LazyImage.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "LazyImage.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "OptimizedImage.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "OptimizedImage.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "OptimizedLazyImage.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "OptimizedLazyImage.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "PageLoader.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "PageLoader.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "PageTransition.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "PageTransition.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "ParallaxSection.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "QualityAssurance.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "QualityAssuranceWrapper.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ResponsiveGrid.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ResponsiveGrid.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "RippleButton.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "RippleButton.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "Section.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "SkeletonLoader.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "SmoothScrollProvider.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "SmoothScrollProvider.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "textarea.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "textarea.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "UnifiedButton.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "UnifiedButton.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "UnifiedCard.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "UnifiedCard.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "UnifiedInput.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "UnifiedInput.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "UnifiedTypography.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "UnifiedTypography.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "WebVitals.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "WebVitals.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "AccessibilityEnhancer.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "AmbientBackground.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "AmbientBackground.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "ContextualCursor.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ContextualCursor.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "KeyboardShortcuts.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "MagneticButton.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "MagneticButton.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "MicroAnimations.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "MicroAnimations.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "NoiseTexture.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "NoiseTexture.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "OpticalTypography.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "OpticalTypography.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "PerformanceMonitor.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ProgressIndicator.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "ProgressIndicator.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "SmartPreloader.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "SmartPreloader.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "SmoothScrollIndicator.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "SmoothScrollIndicator.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "StaggeredReveal.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "StaggeredReveal.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "WellnessProvider.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "WellnessProvider.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "WorldClassProvider.jsx: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "WorldClassProvider.jsx: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "blogPosts.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "eventData.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "eventData.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "investmentData.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "investmentData.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "programData.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "programData.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "retreatsData.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "retreatsData.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "testimonialsData.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "testimonialsData.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "useAdvancedAnimations.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "useAdvancedAnimations.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "useFormValidation.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "useFormValidation.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "useInView.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "useInView.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "advancedSEO.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "advancedSEO.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "errorHandler.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "errorHandler.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "seoAutomation.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "seoAutomation.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "structuredData.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "structuredData.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "utils.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "utils.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "yogaStructuredData.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "yogaStructuredData.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "middleware-redirects.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "middleware-redirects.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility", "middleware.js: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer", "middleware.js: No heading tags found. Proper heading hierarchy is important for SEO and accessibility"], "suggestions": ["page.jsx: Consider adding keywords metadata for better SEO", "page.jsx: Consider adding structured data (JSON-LD) for better search engine understanding", "page.jsx: Consider adding canonical URL to prevent duplicate content issues", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding keywords metadata for better SEO", "page.jsx: Consider adding structured data (JSON-LD) for better search engine understanding", "page.jsx: Consider adding canonical URL to prevent duplicate content issues", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding keywords metadata for better SEO", "page.jsx: Consider adding canonical URL to prevent duplicate content issues", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding keywords metadata for better SEO", "page.jsx: Consider adding canonical URL to prevent duplicate content issues", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding structured data (JSON-LD) for better search engine understanding", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding keywords metadata for better SEO", "page.jsx: Consider adding canonical URL to prevent duplicate content issues", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding structured data (JSON-LD) for better search engine understanding", "page.jsx: Consider adding canonical URL to prevent duplicate content issues", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding keywords metadata for better SEO", "page.jsx: Consider adding structured data (JSON-LD) for better search engine understanding", "page.jsx: Consider adding canonical URL to prevent duplicate content issues", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding keywords metadata for better SEO", "page.jsx: Consider adding structured data (JSON-LD) for better search engine understanding", "page.jsx: Consider adding canonical URL to prevent duplicate content issues", "page.jsx: Consider adding robots metadata to control search engine indexing", "layout.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "page.jsx: Consider adding structured data (JSON-LD) for better search engine understanding", "page.jsx: Consider adding canonical URL to prevent duplicate content issues", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding keywords metadata for better SEO", "page.jsx: Consider adding structured data (JSON-LD) for better search engine understanding", "page.jsx: Consider adding canonical URL to prevent duplicate content issues", "page.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "page.jsx: Consider adding robots metadata to control search engine indexing", "opengraph-image.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "page.jsx: Consider adding keywords metadata for better SEO", "page.jsx: Consider adding canonical URL to prevent duplicate content issues", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding structured data (JSON-LD) for better search engine understanding", "page.jsx: Consider adding canonical URL to prevent duplicate content issues", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding structured data (JSON-LD) for better search engine understanding", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding structured data (JSON-LD) for better search engine understanding", "page.jsx: Consider adding canonical URL to prevent duplicate content issues", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding keywords metadata for better SEO", "page.jsx: Consider adding structured data (JSON-LD) for better search engine understanding", "page.jsx: Consider adding canonical URL to prevent duplicate content issues", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding structured data (JSON-LD) for better search engine understanding", "page.jsx: Consider adding canonical URL to prevent duplicate content issues", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding keywords metadata for better SEO", "page.jsx: Consider adding structured data (JSON-LD) for better search engine understanding", "page.jsx: Consider adding canonical URL to prevent duplicate content issues", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding keywords metadata for better SEO", "page.jsx: Consider adding canonical URL to prevent duplicate content issues", "page.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "page.jsx: Consider adding robots metadata to control search engine indexing", "page.jsx: Consider adding keywords metadata for better SEO", "page.jsx: Consider adding canonical URL to prevent duplicate content issues", "page.jsx: Consider adding robots metadata to control search engine indexing", "ConversionOptimization.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "BookingCalendar.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "CookieConsent.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "CoreWebVitals.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "CriticalCSS.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "FitsseySchedule.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "CustomColorHero.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "OnlineClassesSection.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "LuxuryElements.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "MinimalistHero.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "MinimalistRetreatCard.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "OnlineClassesStyles.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "PerfectNavbar.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "SanityRetreats.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "EnterpriseMetaTags.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "WeatherWidget.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "EnhancedButton.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "GlassCard.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "ParallaxSection.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "SmoothScrollProvider.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "AmbientBackground.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "ContextualCursor.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "KeyboardShortcuts.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "MicroAnimations.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "PerformanceMonitor.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "ProgressIndicator.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "SmoothScrollIndicator.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "WellnessProvider.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)", "WorldClassProvider.jsx: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)"]}}