'use client';
import Image from 'next/image';
import Link from 'next/link';
import React, { useEffect, useState } from 'react';

const ElegantBakasanaHero = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    setIsLoaded(true);

    // Parallax effect
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <section className='relative min-h-screen flex items-center justify-center overflow-hidden'>
      {/* Background Image with Parallax Effect */}
      <div
        className='absolute inset-0 z-0 transform-gpu'
        style={{
          transform: `translateY(${scrollY * 0.5}px) translateZ(0)`,
        }}
      >
        <Image
          src="/images/background/bali-hero.webp" alt="Image"
          alt='BAKASANA - Retreaty jogi Bali i Sri Lanka'
          fill
          className='object-cover object-center scale-110 transition-transform duration-[12s] ease-out'
          priority
          sizes='100vw'
          quality={95}
        />

        {/* Elegant Overlay System */}
        <div className='absolute inset-0 bg-gradient-to-b from-soft-black/40 via-soft-black/25 to-soft-black/50' />
        <div className='absolute inset-0 bg-gradient-to-r from-charcoal/20 via-transparent to-charcoal/20' />
        <div className='absolute inset-0 bg-sanctuary/5' />
      </div>

      {/* Main Content Container */}
      <div
        className={`relative z-20 text-center max-w-7xl mx-auto px-hero-padding lg:px-hero-padding transition-all duration-1200 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-12'
        }`}
      >
        <div className='space-y-10'>
          {/* Elegant Label */}
          <div
            className={`inline-flex items-center gap-sm px-10 py-5 bg-sanctuary/95 backdrop-blur-md border border-charcoal-gold/30 shadow-2xl shadow-soft-black/10 transition-all duration-800 delay-200 hover:bg-pure-white hover:shadow-charcoal-gold/20 ${
              isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
            }`}
          >
            <span className='w-2.5 h-2.5 bg-charcoal-gold rectangular animate-pulse shadow-lg shadow-charcoal-gold/50'></span>
            <span className='text-sm font-inter text-charcoal tracking-[0.25em] uppercase font-medium'>
              RETREAT 2021 • Bali & Sri Lanka
            </span>
            <span className='w-2.5 h-2.5 bg-charcoal-gold rectangular animate-pulse shadow-lg shadow-charcoal-gold/50'></span>
          </div>

          {/* Majestic Title */}
          <h1
            className={`font-cormorant text-8xl md:text-9xl lg:text-[9rem] xl:text-[10rem] font-light text-sanctuary leading-[0.85] tracking-[0.2em] drop-shadow-2xl transition-all duration-1200 delay-400 hover:tracking-[0.25em] ${
              isLoaded
                ? 'opacity-100 translate-y-0'
                : 'opacity-0 translate-y-12'
            }`}
          >
            BAKASANA
          </h1>

          {/* Poetic Subtitle */}
          <p
            className={`text-2xl md:text-3xl lg:text-4xl text-sanctuary/95 font-cormorant italic font-light tracking-wide drop-shadow-lg transition-all duration-1200 delay-600 ${
              isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
            }`}
          >
            ~ jóga jest drogą ciszy ~
          </p>

          {/* Elegant Description */}
          <p
            className={`text-lg md:text-xl lg:text-2xl text-sanctuary/85 max-w-4xl mx-auto leading-relaxed font-inter font-light drop-shadow-md transition-all duration-1200 delay-700 ${
              isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
            }`}
          >
            Odkryj transformującą moc jogi w duchowych sercach Azji. Dołącz do
            naszej autentycznej podróży przez terasy ryżowe Ubud, świątynie Bali
            i tajemnicze krajobrazy Sri Lanki.
          </p>

          {/* Premium Statistics Grid */}
          <div
            className={`grid grid-cols-2 md:grid-cols-4 gap-lg lg:gap-xl max-w-5xl mx-auto py-12 transition-all duration-1200 delay-800 ${
              isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
            }`}
          >
            {[
              { number: '24+', label: 'miejsca', delay: '0s' },
              { number: '10', label: 'lat doświadczenia', delay: '0.1s' },
              {
                number: '500+',
                label: 'zadowolonych uczestników',
                delay: '0.2s',
              },
              { number: 'A+', label: 'ocena satysfakcji', delay: '0.3s' },
            ].map((stat, index) => (
              <div
                key={index}
                className='text-center group cursor-default'
                style={{ animationDelay: stat.delay }}
              >
                <div className='relative'>
                  <div className='text-4xl md:text-5xl lg:text-6xl font-cormorant font-medium text-charcoal-gold mb-3 drop-shadow-lg group-hover:scale-110 transition-all duration-500 group-hover:text-terra-amber /* TODO: Replace with HeroTitle */ /* TODO: Replace with HeroTitle */ /* TODO: Replace with HeroTitle */'>
                    {stat.number}
                  </div>
                  <div className='absolute inset-0 text-4xl md:text-5xl lg:text-6xl font-cormorant font-medium text-charcoal-gold/20 blur-sm -z-10'>
                    {stat.number}
                  </div>
                </div>
                <div className='text-sm md:text-base lg:text-lg text-sanctuary/75 font-inter tracking-wide group-hover:text-sanctuary transition-colors duration-300'>
                  {stat.label}
                </div>
              </div>
            ))}
          </div>

          {/* Premium CTA Buttons */}
          <div
            className={`flex flex-col sm:flex-row gap-lg justify-center items-center pt-8 transition-all duration-1200 delay-1000 ${
              isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
            }`}
          >
            {/* Elegant Ghost Button */}
            <Link
              href='/program'
              className='group relative inline-flex items-center gap-sm px-12 py-5 bg-transparent text-sanctuary border-2 border-sanctuary/70 font-inter font-medium tracking-wide transition-all duration-500 hover:bg-sanctuary hover:text-charcoal hover:border-sanctuary hover:shadow-2xl hover:shadow-sanctuary/30 backdrop-blur-md overflow-hidden'
            >
              <div className='absolute inset-0 bg-sanctuary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left'></div>
              <span className='relative z-10'>Przegląd harmonogramu</span>
              <svg
                className='relative z-10 w-5 h-5 transition-transform group-hover:translate-x-2'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z'
                />
              </svg>
            </Link>

            {/* Premium Filled Button */}
            <Link
              href='/rezerwacja'
              className='group relative inline-flex items-center gap-sm px-12 py-5 bg-charcoal-gold text-sanctuary font-inter font-medium tracking-wide transition-all duration-500 hover:bg-terra-amber hover:shadow-2xl hover:shadow-charcoal-gold/40 hover:scale-105 backdrop-blur-md overflow-hidden'
            >
              <div className='absolute inset-0 bg-gradient-to-r from-charcoal-gold to-golden-amber opacity-0 group-hover:opacity-100 transition-opacity duration-500'></div>
              <span className='relative z-10'>Rezerwuj</span>
              <svg
                className='relative z-10 w-5 h-5 transition-transform group-hover:translate-x-2'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M9 5l7 7-7 7'
                />
              </svg>
            </Link>
          </div>
        </div>
      </div>

      {/* Floating Decorative Elements - Enhanced */}
      <div className='absolute inset-0 z-10 pointer-events-none overflow-hidden'>
        {/* Elegant floating particles */}
        <div
          className='absolute top-1/4 left-1/4 w-4 h-4 bg-charcoal-gold/50 rectangular shadow-lg shadow-charcoal-gold/30'
          style={{
            animation: 'float 8s ease-in-out infinite',
          }}
        />
        <div
          className='absolute top-1/3 right-1/3 w-3 h-3 bg-sanctuary/60 rectangular shadow-lg shadow-sanctuary/20'
          style={{
            animation: 'float 10s ease-in-out infinite',
            animationDelay: '2s',
          }}
        />
        <div
          className='absolute bottom-1/3 left-1/3 w-3.5 h-3.5 bg-charcoal-gold/40 rectangular shadow-lg shadow-charcoal-gold/20'
          style={{
            animation: 'float 9s ease-in-out infinite',
            animationDelay: '4s',
          }}
        />
        <div
          className='absolute top-2/3 right-1/4 w-2 h-2 bg-sanctuary/70 rectangular shadow-lg shadow-sanctuary/30'
          style={{
            animation: 'float 11s ease-in-out infinite',
            animationDelay: '1s',
          }}
        />
        <div
          className='absolute top-1/2 left-1/6 w-2.5 h-2.5 bg-charcoal-gold/30 rectangular shadow-lg shadow-charcoal-gold/20'
          style={{
            animation: 'float 7s ease-in-out infinite',
            animationDelay: '3s',
          }}
        />
        <div
          className='absolute bottom-1/4 right-1/6 w-3 h-3 bg-sanctuary/50 rectangular shadow-lg shadow-sanctuary/20'
          style={{
            animation: 'float 12s ease-in-out infinite',
            animationDelay: '5s',
          }}
        />
      </div>

      {/* Elegant Scroll Indicator */}
      <div
        className={`absolute bottom-12 left-1/2 transform -translate-x-1/2 z-20 transition-all duration-1200 delay-1200 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
        }`}
      >
        <div className='flex flex-col items-center text-sanctuary/80 group cursor-pointer'>
          <span className='text-xs mb-sm font-inter tracking-[0.3em] uppercase font-light group-hover:text-sanctuary transition-colors duration-300'>
            Odkryj więcej
          </span>
          <div className='w-px h-12 bg-gradient-to-b from-sanctuary/50 to-transparent animate-pulse'></div>
          <svg
            className='w-6 h-6 mt-3 animate-bounce group-hover:text-charcoal-gold transition-colors duration-300'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={1.5}
              d='M19 14l-7 7m0 0l-7-7m7 7V3'
            />
          </svg>
        </div>
      </div>

      {/* Subtle Gradient Vignette */}
      <div className='absolute inset-0 z-15 pointer-events-none'>
        <div className='absolute inset-0 bg-gradient-to-t from-soft-black/20 via-transparent to-transparent'></div>
        <div className='absolute inset-0 bg-gradient-to-b from-soft-black/10 via-transparent to-transparent'></div>
      </div>
    </section>
  );
};

export default ElegantBakasanaHero;
