'use client';

import { useEffect, useCallback } from 'react';
import { usePathname } from 'next/navigation';

export default function ErrorTracking() {
  const pathname = usePathname();

  // Enhanced error tracking with context
  const trackError = useCallback((error, context = {}) => {
    const errorData = {
      message: error.message,
      stack: error.stack,
      name: error.name,
      page: pathname,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      ...context
    };

    // Send to Sentry with enhanced context
    if (window.Sentry) {
      window.Sentry.withScope((scope) => {
        scope.setTag('component', context.component || 'unknown');
        scope.setTag('page', pathname);
        scope.setLevel(context.level || 'error');
        
        scope.setContext('error_details', {
          error_type: error.constructor.name,
          error_message: error.message,
          page_path: pathname,
          user_action: context.userAction,
          retreat_context: context.retreatContext
        });

        scope.setContext('user_context', {
          user_agent: navigator.userAgent,
          language: navigator.language,
          platform: navigator.platform,
          connection: navigator.connection ? {
            effectiveType: navigator.connection.effectiveType,
            downlink: navigator.connection.downlink
          } : null
        });

        if (context.userId) {
          scope.setUser({ id: context.userId });
        }

        window.Sentry.captureException(error);
      });
    }

    // Send to Google Analytics
    if (window.gtag) {
      window.gtag('event', 'exception', {
        description: error.message,
        fatal: context.fatal || false,
        event_category: 'JavaScript Error',
        event_label: context.component || pathname,
        custom_parameter_1: error.name,
        custom_parameter_2: context.userAction || 'unknown'
      });
    }

    // Send to Mixpanel
    if (window.mixpanel) {
      window.mixpanel.track('JavaScript Error', {
        ...errorData,
        error_category: context.category || 'general',
        user_action: context.userAction,
        retreat_context: context.retreatContext
      });
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error tracked:', errorData);
    }
  }, [pathname]);

  // Track unhandled promise rejections
  const trackUnhandledRejection = useCallback((event) => {
    const error = event.reason instanceof Error ? event.reason : new Error(event.reason);
    
    trackError(error, {
      component: 'UnhandledPromiseRejection',
      category: 'promise_rejection',
      fatal: false,
      userAction: 'automatic'
    });
  }, [trackError]);

  // Track global JavaScript errors
  const trackGlobalError = useCallback((event) => {
    const error = event.error || new Error(event.message);
    
    trackError(error, {
      component: 'GlobalErrorHandler',
      category: 'javascript_error',
      fatal: true,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      userAction: 'automatic'
    });
  }, [trackError]);

  // Track network errors
  const trackNetworkError = useCallback((url, method, status, error) => {
    const networkError = new Error(`Network request failed: ${method} ${url} - ${status}`);
    
    trackError(networkError, {
      component: 'NetworkRequest',
      category: 'network_error',
      fatal: false,
      network_details: {
        url,
        method,
        status,
        error_message: error?.message
      },
      userAction: 'api_call'
    });
  }, [trackError]);

  // Track booking-specific errors
  const trackBookingError = useCallback((error, bookingData) => {
    trackError(error, {
      component: 'BookingSystem',
      category: 'booking_error',
      fatal: true,
      retreatContext: {
        retreat_name: bookingData?.retreatName,
        booking_step: bookingData?.step,
        form_data: bookingData?.formData,
        payment_method: bookingData?.paymentMethod
      },
      userAction: 'booking_attempt',
      userId: bookingData?.userId
    });
  }, [trackError]);

  // Track form validation errors
  const trackFormError = useCallback((error, formData) => {
    trackError(error, {
      component: 'FormValidation',
      category: 'form_error',
      fatal: false,
      form_context: {
        form_name: formData?.formName,
        field_name: formData?.fieldName,
        field_value: formData?.fieldValue,
        validation_rule: formData?.validationRule
      },
      userAction: 'form_submission'
    });
  }, [trackError]);

  // Track API errors with retry information
  const trackAPIError = useCallback((error, apiData) => {
    trackError(error, {
      component: 'APIClient',
      category: 'api_error',
      fatal: false,
      api_context: {
        endpoint: apiData?.endpoint,
        method: apiData?.method,
        status_code: apiData?.statusCode,
        retry_count: apiData?.retryCount,
        response_time: apiData?.responseTime
      },
      userAction: 'api_request'
    });
  }, [trackError]);

  // Track performance-related errors
  const trackPerformanceError = useCallback((error, performanceData) => {
    trackError(error, {
      component: 'PerformanceMonitoring',
      category: 'performance_error',
      fatal: false,
      performance_context: {
        metric_name: performanceData?.metricName,
        metric_value: performanceData?.metricValue,
        threshold: performanceData?.threshold,
        page_load_time: performanceData?.pageLoadTime
      },
      userAction: 'performance_monitoring'
    });
  }, [trackError]);

  // Set up global error handlers
  useEffect(() => {
    // Global error handler
    window.addEventListener('error', trackGlobalError);
    
    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', trackUnhandledRejection);

    // Expose error tracking functions globally
    window.trackRetreatError = trackError;
    window.trackBookingError = trackBookingError;
    window.trackFormError = trackFormError;
    window.trackAPIError = trackAPIError;
    window.trackNetworkError = trackNetworkError;
    window.trackPerformanceError = trackPerformanceError;

    // Override fetch to track network errors
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const startTime = performance.now();
      try {
        const response = await originalFetch(...args);
        const endTime = performance.now();
        
        // Track slow API calls
        const responseTime = endTime - startTime;
        if (responseTime > 5000) { // 5 seconds
          trackPerformanceError(new Error('Slow API response'), {
            metricName: 'api_response_time',
            metricValue: responseTime,
            threshold: 5000,
            endpoint: args[0]
          });
        }

        // Track HTTP errors
        if (!response.ok) {
          trackNetworkError(
            args[0],
            args[1]?.method || 'GET',
            response.status,
            new Error(`HTTP ${response.status}: ${response.statusText}`)
          );
        }

        return response;
      } catch (error) {
        const endTime = performance.now();
        trackNetworkError(
          args[0],
          args[1]?.method || 'GET',
          0,
          error
        );
        throw error;
      }
    };

    // Cleanup
    return () => {
      window.removeEventListener('error', trackGlobalError);
      window.removeEventListener('unhandledrejection', trackUnhandledRejection);
      window.fetch = originalFetch;
    };
  }, [trackGlobalError, trackUnhandledRejection, trackError, trackBookingError, trackFormError, trackAPIError, trackNetworkError, trackPerformanceError]);

  // Track route changes for context
  useEffect(() => {
    if (window.Sentry) {
      window.Sentry.addBreadcrumb({
        message: `Navigated to ${pathname}`,
        category: 'navigation',
        level: 'info',
        data: {
          from: document.referrer,
          to: pathname
        }
      });
    }
  }, [pathname]);

  // This component doesn't render anything
  return null;
}
