{"version": 3, "file": "matchers-jestonly.js", "sources": ["../../src/jest/matchers/to-match-codeframe.ts", "../../src/jest/matchers/to-match-inline-codeframe.ts"], "sourcesContent": ["import kleur from \"kleur\";\nimport { toMatchSnapshot } from \"jest-snapshot\";\nimport { codeframe, type CodeframeOptions } from \"../../formatters/codeframe\";\nimport { type Report } from \"../../reporter\";\nimport {\n\ttype MatcherContext,\n\ttype MatcherR<PERSON>ult,\n\ttype MaybeAsyncCallback,\n\tdiverge,\n} from \"../utils\";\nimport { getResults } from \"./get-results\";\n\nconst options: CodeframeOptions = {\n\tshowLink: false,\n\tshowSummary: false,\n\tshowSelector: true,\n};\n\nfunction createMatcher(): MaybeAsyncCallback<Report | string, [Array<string | object>]> {\n\tfunction toMatchCodeframe(\n\t\tthis: MatcherContext,\n\t\tactual: Report | string,\n\t\t...rest: Array<string | object>\n\t): MatcherResult {\n\t\t/* istanbul ignore next: cant figure out when this would be unset */\n\t\tconst filename = this.testPath ?? \"inline\";\n\t\tconst results = getResults(filename, actual);\n\t\tconst enabled = kleur.enabled;\n\t\tkleur.enabled = false;\n\t\tconst snapshot = codeframe(results, options).replace(/\\s+$/gm, \"\");\n\t\tkleur.enabled = enabled;\n\n\t\t/* eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call --\n\t\t * the implementation works but the declarations doesn't allow it */\n\t\treturn (toMatchSnapshot as any).call(this, snapshot, ...rest);\n\t}\n\treturn diverge(toMatchCodeframe);\n}\n\nexport { createMatcher as toMatchCodeframe };\n", "import kleur from \"kleur\";\nimport { toMatchInlineSnapshot } from \"jest-snapshot\";\nimport { codeframe, type CodeframeOptions } from \"../../formatters/codeframe\";\nimport { type Report } from \"../../reporter\";\nimport { type MatcherContext, type MatcherResult, isThenable } from \"../utils\";\nimport { getResults } from \"./get-results\";\n\nconst options: CodeframeOptions = {\n\tshowLink: false,\n\tshowSummary: false,\n\tshowSelector: true,\n};\n\nfunction toMatchInlineCodeframeImpl(\n\tcontext: MatcherContext,\n\tactual: Report | string,\n\t...rest: Array<string | object>\n): MatcherResult {\n\t/* istanbul ignore next: cant figure out when this would be unset */\n\tconst filename = context.testPath ?? \"inline\";\n\tconst results = getResults(filename, actual);\n\tconst enabled = kleur.enabled;\n\tkleur.enabled = false;\n\tconst snapshot = codeframe(results, options).replace(/\\s+$/gm, \"\");\n\tkleur.enabled = enabled;\n\n\t/* eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call --\n\t * the implementation works but the declarations doesn't allow it */\n\treturn (toMatchInlineSnapshot as any).call(context, snapshot, ...rest);\n}\n\ntype ToMatchInlineCodeframeMatcher = (\n\tthis: MatcherContext,\n\tactual: Report | Promise<Report> | string,\n\t...rest: Array<string | object>\n) => MatcherResult | Promise<MatcherResult>;\n\nfunction createMatcher(): ToMatchInlineCodeframeMatcher {\n\tfunction toMatchInlineCodeframe(\n\t\tthis: MatcherContext,\n\t\tactual: Report | Promise<Report> | string,\n\t\t...rest: Array<string | object>\n\t): MatcherResult | Promise<MatcherResult> {\n\t\tconst context = {\n\t\t\t...this,\n\n\t\t\t/* Capture the original stack frames as they are needed by \"jest-snapshot\"\n\t\t\t * to determine where to write the inline snapshots. When resolving the\n\t\t\t * promise the original stack frames are lost and the snapshot will be\n\t\t\t * written in this files instaed. */\n\t\t\terror: new Error(),\n\t\t};\n\n\t\tif (isThenable(actual)) {\n\t\t\treturn actual.then((resolved) => toMatchInlineCodeframeImpl(context, resolved, ...rest));\n\t\t} else {\n\t\t\treturn toMatchInlineCodeframeImpl(context, actual, ...rest);\n\t\t}\n\t}\n\n\treturn toMatchInlineCodeframe;\n}\n\nexport { createMatcher as toMatchInlineCodeframe };\n"], "names": ["options", "createMatcher", "getResults", "kleur", "codeframe", "toMatchSnapshot", "diverge", "toMatchInlineSnapshot", "isThenable"], "mappings": ";;;;;;;;;;;;AAYA,MAAMA,SAAA,GAA4B;AAAA,EACjC,QAAA,EAAU,KAAA;AAAA,EACV,WAAA,EAAa,KAAA;AAAA,EACb,YAAA,EAAc;AACf,CAAA;AAEA,SAASC,eAAA,GAA+E;AACvF,EAAA,SAAS,gBAAA,CAER,WACG,IAAA,EACa;AAEhB,IAAA,MAAM,QAAA,GAAW,KAAK,QAAA,IAAY,QAAA;AAClC,IAAA,MAAM,OAAA,GAAUC,mBAAA,CAAW,QAAA,EAAU,MAAM,CAAA;AAC3C,IAAA,MAAM,UAAUC,sBAAA,CAAM,OAAA;AACtB,IAAAA,sBAAA,CAAM,OAAA,GAAU,KAAA;AAChB,IAAA,MAAM,WAAWC,cAAA,CAAU,OAAA,EAASJ,SAAO,CAAA,CAAE,OAAA,CAAQ,UAAU,EAAE,CAAA;AACjE,IAAAG,sBAAA,CAAM,OAAA,GAAU,OAAA;AAIhB,IAAA,OAAQE,4BAAA,CAAwB,IAAA,CAAK,IAAA,EAAM,QAAA,EAAU,GAAG,IAAI,CAAA;AAAA;AAE7D,EAAA,OAAOC,qBAAQ,gBAAgB,CAAA;AAChC;;AC9BA,MAAM,OAAA,GAA4B;AAAA,EACjC,QAAA,EAAU,KAAA;AAAA,EACV,WAAA,EAAa,KAAA;AAAA,EACb,YAAA,EAAc;AACf,CAAA;AAEA,SAAS,0BAAA,CACR,OAAA,EACA,MAAA,EAAA,GACG,IAAA,EACa;AAEhB,EAAA,MAAM,QAAA,GAAW,QAAQ,QAAA,IAAY,QAAA;AACrC,EAAA,MAAM,OAAA,GAAUJ,mBAAA,CAAW,QAAA,EAAU,MAAM,CAAA;AAC3C,EAAA,MAAM,UAAUC,sBAAA,CAAM,OAAA;AACtB,EAAAA,sBAAA,CAAM,OAAA,GAAU,KAAA;AAChB,EAAA,MAAM,WAAWC,cAAA,CAAU,OAAA,EAAS,OAAO,CAAA,CAAE,OAAA,CAAQ,UAAU,EAAE,CAAA;AACjE,EAAAD,sBAAA,CAAM,OAAA,GAAU,OAAA;AAIhB,EAAA,OAAQI,kCAAA,CAA8B,IAAA,CAAK,OAAA,EAAS,QAAA,EAAU,GAAG,IAAI,CAAA;AACtE;AAQA,SAAS,aAAA,GAA+C;AACvD,EAAA,SAAS,sBAAA,CAER,WACG,IAAA,EACsC;AACzC,IAAA,MAAM,OAAA,GAAU;AAAA,MACf,GAAG,IAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMH,KAAA,EAAO,IAAI,KAAA;AAAM,KAClB;AAEA,IAAA,IAAIC,uBAAA,CAAW,MAAM,CAAA,EAAG;AACvB,MAAA,OAAO,MAAA,CAAO,KAAK,CAAC,QAAA,KAAa,2BAA2B,OAAA,EAAS,QAAA,EAAU,GAAG,IAAI,CAAC,CAAA;AAAA,KACxF,MAAO;AACN,MAAA,OAAO,0BAAA,CAA2B,OAAA,EAAS,MAAA,EAAQ,GAAG,IAAI,CAAA;AAAA;AAC3D;AAGD,EAAA,OAAO,sBAAA;AACR;;;;;"}