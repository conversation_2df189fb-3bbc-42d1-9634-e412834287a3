{"name": "html-minifier-terser", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "version": "7.2.0", "license": "MIT", "repository": "https://github.com/terser/html-minifier-terser.git", "bugs": "https://github.com/terser/html-minifier-terser/issues", "homepage": "https://terser.org/html-minifier-terser/", "author": "<PERSON>", "maintainers": ["<PERSON> <<EMAIL>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>> (http://perfectionkills.com/)", "<PERSON><PERSON><PERSON> <sibiraj.dev>"], "contributors": ["<PERSON> (https://github.com/gilmoreorless)", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "keywords": ["cli", "compress", "compressor", "css", "html", "htmlmin", "javascript", "min", "minification", "minifier", "minify", "optimize", "optimizer", "pack", "packer", "parse", "parser", "terser", "uglifier", "uglify"], "engines": {"node": "^14.13.1 || >=16.0.0"}, "type": "module", "main": "./dist/htmlminifier.cjs", "module": "./src/htmlminifier.js", "exports": {".": {"require": "./dist/htmlminifier.cjs", "import": "./src/htmlminifier.js"}, "./dist/*": "./dist/*.js", "./package.json": "./package.json"}, "bin": {"html-minifier-terser": "./cli.js"}, "files": ["dist/", "src/", "cli.js"], "scripts": {"build": "rollup -c", "test:node": "NODE_OPTIONS=--experimental-vm-modules jest --verbose", "test:web": "NODE_OPTIONS=--experimental-vm-modules jest --verbose --environment=jsdom", "test:watch": "NODE_OPTIONS=--experimental-vm-modules jest verbose --watch", "test": "npm run test:node", "serve": "vite", "build:docs": "vite build --base /html-minifier-terser/", "lint": "eslint . --ignore-path .gitignore", "prepare": "is-ci || husky install"}, "dependencies": {"camel-case": "^4.1.2", "clean-css": "~5.3.2", "commander": "^10.0.0", "entities": "^4.4.0", "param-case": "^3.0.4", "relateurl": "^0.2.7", "terser": "^5.15.1"}, "devDependencies": {"@commitlint/cli": "^17.5.1", "@jest/globals": "^29.5.0", "@rollup/plugin-commonjs": "^24.0.1", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.0.2", "@rollup/plugin-terser": "^0.4.1", "alpinejs": "^3.12.0", "commitlint-config-non-conventional": "^1.0.1", "eslint": "^8.38.0", "eslint-config-standard": "^17.0.0", "husky": "^8.0.3", "is-ci": "^3.0.1", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "lint-staged": "^13.2.1", "rollup": "^3.20.2", "rollup-plugin-polyfill-node": "^0.12.0", "vite": "^4.2.1"}}