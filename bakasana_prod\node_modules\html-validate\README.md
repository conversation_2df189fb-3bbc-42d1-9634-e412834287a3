# html-validate

[![pipeline status](https://gitlab.com/html-validate/html-validate/badges/master/pipeline.svg)](https://gitlab.com/html-validate/html-validate/commits/master)
[![coverage report](https://gitlab.com/html-validate/html-validate/badges/master/coverage.svg)](https://gitlab.com/html-validate/html-validate/commits/master)

Offline HTML5 validator. Validates either a full document or a smaller
(incomplete) template, e.g. from an AngularJS or Vue.js component.

Read the full documentation at https://html-validate.org/

## Features

- Can test fragments of HTML, for instance a component template.
- Does not upload any data to a remote server, all testing is done locally.
- Strict and non-forgiving parsing. It will not try to correct any incorrect
  markup or guess what it should do.

## Usage

    npm install -g html-validate
    html-validate [OPTIONS] [FILENAME..] [DIR..]

## Configuration

Create `.htmlvalidate.json`:

```js
{
  "extends": [
    "html-validate:recommended"
  ],

  "rules": {
    "close-order": "error",
    "void": ["warn", {"style": "omit"}]
  }
}
```

## Example

```html
<p>
  <button>Click me!</button>
  <div id="show-me">
    Lorem ipsum
  </div>
</p>
```

```text
  1:1  error  Element <p> is implicitly closed by adjacent <div>  no-implicit-close
  2:2  error  Button is missing type attribute                    button-type
  6:4  error  Unexpected close-tag, expected opening tag          close-order
```

## Bundles

The library comes in four flavours:

- CommonJS full (`dist/cjs/index.js`)
- CommonJS browser (`dist/cjs/browser.js`)
- ESM full (`dist/es/index.js`)
- ESM browser (`dist/es/browser.js`)

The browser bundle contains a slimmed version without CLI and NodeJS dependencies.

```ts
/* automatically determine build based on `browser` export condition */
import { ... } from "html-validate";

/* explicitly use nodejs bundle */
import { ... } from "html-validate/node";

/* explicitly use browser bundle */
import { ... } from "html-validate/browser";
```

See [running in browser](https://html-validate.org/dev/running-in-browser.html) for details about getting HTML-Validate running in a browser environment.

## Developing

### Prerequisites

- NodeJS 18
- NPM 7

### Test

Testing is done using jest.

    npm test

or call `jest` directly.

Some tests are autogenerated from documentation examples, use `npm run docs` to build those before running.

### Lint

Linting is done using ESLint.

    npm run eslint

or call `eslint` directly.

### Build

    npm run build

To build documentation use:

    npm run docs

The documentation can be served locally using:

    npm start
