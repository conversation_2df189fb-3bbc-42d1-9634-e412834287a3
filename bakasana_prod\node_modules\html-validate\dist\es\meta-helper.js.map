{"version": 3, "file": "meta-helper.js", "sources": ["../../src/meta/define-metadata.ts", "../../src/meta/helper.ts"], "sourcesContent": ["import { type MetaDataTable } from \"./element\";\n\n/**\n * Helper function to assist IDE with completion and type-checking.\n *\n * @public\n */\nexport function defineMetadata(metatable: MetaDataTable): MetaDataTable {\n\treturn metatable;\n}\n", "import { naturalJoin } from \"../utils/natural-join\";\nimport { type MetaAttributeAllowedCallback } from \"./element\";\nimport { type HtmlElementLike } from \"./html-element-like\";\n\n/**\n * Helpers when writing element metadata.\n *\n * @public\n */\nexport interface MetadataHelper {\n\t/** Returns an error if another attribute is omitted, i.e. it requires another attribute to be present to pass. */\n\tallowedIfAttributeIsPresent(this: void, ...attr: string[]): MetaAttributeAllowedCallback;\n\n\t/** Returns an error if another attribute is present, i.e. it requires another attribute to be omitted to pass. */\n\tallowedIfAttributeIsAbsent(this: void, ...attr: string[]): MetaAttributeAllowedCallback;\n\n\t/** Returns an error if another attribute does not have one of the listed values */\n\tallowedIfAttributeHasValue(\n\t\tthis: void,\n\t\tattr: string,\n\t\tvalue: string[],\n\t\toptions?: { defaultValue?: string | null },\n\t): MetaAttributeAllowedCallback;\n\n\t/**\n\t * Returns an error if the node doesn't have any of the given elements as parent\n\t *\n\t * @since 8.2.0\n\t **/\n\tallowedIfParentIsPresent(this: void, ...tags: string[]): MetaAttributeAllowedCallback;\n}\n\n/**\n * @internal\n */\nexport function allowedIfAttributeIsPresent(...attr: string[]) {\n\treturn (node: HtmlElementLike) => {\n\t\tif (attr.some((it) => node.hasAttribute(it))) {\n\t\t\treturn null;\n\t\t}\n\t\tconst expected = naturalJoin(attr.map((it) => `\"${it}\"`));\n\t\treturn `requires ${expected} attribute to be present`;\n\t};\n}\n\n/**\n * @internal\n */\nexport function allowedIfAttributeIsAbsent(...attr: string[]): MetaAttributeAllowedCallback {\n\treturn (node: HtmlElementLike) => {\n\t\tconst present = attr.filter((it) => node.hasAttribute(it));\n\t\tif (present.length === 0) {\n\t\t\treturn null;\n\t\t}\n\t\tconst expected = naturalJoin(present.map((it) => `\"${it}\"`));\n\t\treturn `cannot be used at the same time as ${expected}`;\n\t};\n}\n\n/**\n * @internal\n */\nexport function allowedIfAttributeHasValue(\n\tkey: string,\n\texpectedValue: string[],\n\t{ defaultValue }: { defaultValue?: string | null } = {},\n): MetaAttributeAllowedCallback {\n\treturn (node: HtmlElementLike) => {\n\t\tconst attr = node.getAttribute(key);\n\t\tif (attr && typeof attr !== \"string\") {\n\t\t\treturn null;\n\t\t}\n\t\tconst actualValue = attr ?? defaultValue;\n\t\tif (actualValue && expectedValue.includes(actualValue.toLocaleLowerCase())) {\n\t\t\treturn null;\n\t\t}\n\t\tconst expected = naturalJoin(expectedValue.map((it) => `\"${it}\"`));\n\t\treturn `\"${key}\" attribute must be ${expected}`;\n\t};\n}\n\n/**\n * @internal\n */\nexport function allowedIfParentIsPresent(\n\tthis: void,\n\t...tags: string[]\n): MetaAttributeAllowedCallback {\n\treturn (node: HtmlElementLike) => {\n\t\tconst match = tags.some((it) => node.closest(it));\n\t\tif (match) {\n\t\t\treturn null;\n\t\t}\n\t\tconst expected = naturalJoin(tags.map((it) => `<${it}>`));\n\t\treturn `requires ${expected} as parent`;\n\t};\n}\n\n/**\n * @public\n */\nexport const metadataHelper: MetadataHelper = {\n\tallowedIfAttributeIsPresent,\n\tallowedIfAttributeIsAbsent,\n\tallowedIfAttributeHasValue,\n\tallowedIfParentIsPresent,\n};\n"], "names": [], "mappings": ";;AAOO,SAAS,eAAe,SAAA,EAAyC;AACvE,EAAA,OAAO,SAAA;AACR;;AC0BO,SAAS,+BAA+B,IAAA,EAAgB;AAC9D,EAAA,OAAO,CAAC,IAAA,KAA0B;AACjC,IAAA,IAAI,IAAA,CAAK,KAAK,CAAC,EAAA,KAAO,KAAK,YAAA,CAAa,EAAE,CAAC,CAAA,EAAG;AAC7C,MAAA,OAAO,IAAA;AAAA;AAER,IAAA,MAAM,QAAA,GAAW,YAAY,IAAA,CAAK,GAAA,CAAI,CAAC,EAAA,KAAO,CAAA,CAAA,EAAI,EAAE,CAAA,CAAA,CAAG,CAAC,CAAA;AACxD,IAAA,OAAO,YAAY,QAAQ,CAAA,wBAAA,CAAA;AAAA,GAC5B;AACD;AAKO,SAAS,8BAA8B,IAAA,EAA8C;AAC3F,EAAA,OAAO,CAAC,IAAA,KAA0B;AACjC,IAAA,MAAM,OAAA,GAAU,KAAK,MAAA,CAAO,CAAC,OAAO,IAAA,CAAK,YAAA,CAAa,EAAE,CAAC,CAAA;AACzD,IAAA,IAAI,OAAA,CAAQ,WAAW,CAAA,EAAG;AACzB,MAAA,OAAO,IAAA;AAAA;AAER,IAAA,MAAM,QAAA,GAAW,YAAY,OAAA,CAAQ,GAAA,CAAI,CAAC,EAAA,KAAO,CAAA,CAAA,EAAI,EAAE,CAAA,CAAA,CAAG,CAAC,CAAA;AAC3D,IAAA,OAAO,sCAAsC,QAAQ,CAAA,CAAA;AAAA,GACtD;AACD;AAKO,SAAS,2BACf,GAAA,EACA,aAAA,EACA,EAAE,YAAA,EAAa,GAAsC,EAAC,EACvB;AAC/B,EAAA,OAAO,CAAC,IAAA,KAA0B;AACjC,IAAA,MAAM,IAAA,GAAO,IAAA,CAAK,YAAA,CAAa,GAAG,CAAA;AAClC,IAAA,IAAI,IAAA,IAAQ,OAAO,IAAA,KAAS,QAAA,EAAU;AACrC,MAAA,OAAO,IAAA;AAAA;AAER,IAAA,MAAM,cAAc,IAAA,IAAQ,YAAA;AAC5B,IAAA,IAAI,eAAe,aAAA,CAAc,QAAA,CAAS,WAAA,CAAY,iBAAA,EAAmB,CAAA,EAAG;AAC3E,MAAA,OAAO,IAAA;AAAA;AAER,IAAA,MAAM,QAAA,GAAW,YAAY,aAAA,CAAc,GAAA,CAAI,CAAC,EAAA,KAAO,CAAA,CAAA,EAAI,EAAE,CAAA,CAAA,CAAG,CAAC,CAAA;AACjE,IAAA,OAAO,CAAA,CAAA,EAAI,GAAG,CAAA,oBAAA,EAAuB,QAAQ,CAAA,CAAA;AAAA,GAC9C;AACD;AAKO,SAAS,4BAEZ,IAAA,EAC4B;AAC/B,EAAA,OAAO,CAAC,IAAA,KAA0B;AACjC,IAAA,MAAM,KAAA,GAAQ,KAAK,IAAA,CAAK,CAAC,OAAO,IAAA,CAAK,OAAA,CAAQ,EAAE,CAAC,CAAA;AAChD,IAAA,IAAI,KAAA,EAAO;AACV,MAAA,OAAO,IAAA;AAAA;AAER,IAAA,MAAM,QAAA,GAAW,YAAY,IAAA,CAAK,GAAA,CAAI,CAAC,EAAA,KAAO,CAAA,CAAA,EAAI,EAAE,CAAA,CAAA,CAAG,CAAC,CAAA;AACxD,IAAA,OAAO,YAAY,QAAQ,CAAA,UAAA,CAAA;AAAA,GAC5B;AACD;AAKO,MAAM,cAAA,GAAiC;AAAA,EAC7C,2BAAA;AAAA,EACA,0BAAA;AAAA,EACA,0BAAA;AAAA,EACA;AACD;;;;"}