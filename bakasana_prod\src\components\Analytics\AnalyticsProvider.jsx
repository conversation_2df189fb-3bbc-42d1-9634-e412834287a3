'use client';

import { Suspense } from 'react';
import dynamic from 'next/dynamic';

// Lazy load analytics components for better performance
const EnhancedAnalytics = dynamic(() => import('./EnhancedAnalytics'), {
  ssr: false,
  loading: () => null
});

const ErrorTracking = dynamic(() => import('./ErrorTracking'), {
  ssr: false,
  loading: () => null
});

const PerformanceMonitoring = dynamic(() => import('../Performance/PerformanceMonitoring'), {
  ssr: false,
  loading: () => null
});

// Check if analytics should be loaded (respecting user privacy)
function shouldLoadAnalytics() {
  if (typeof window === 'undefined') return false;
  
  // Check for Do Not Track
  if (navigator.doNotTrack === '1' || window.doNotTrack === '1') {
    return false;
  }
  
  // Check for user consent (you can implement your own consent logic)
  const consent = localStorage.getItem('analytics-consent');
  if (consent === 'false') {
    return false;
  }
  
  // Default to true if no explicit rejection
  return true;
}

export default function AnalyticsProvider({ children }) {
  const loadAnalytics = shouldLoadAnalytics();

  return (
    <>
      {children}
      
      {loadAnalytics && (
        <Suspense fallback={null}>
          {/* Core Analytics */}
          <EnhancedAnalytics />
          
          {/* Error Tracking */}
          <ErrorTracking />
          
          {/* Performance Monitoring */}
          <PerformanceMonitoring />
        </Suspense>
      )}
    </>
  );
}

// Export utility functions for manual tracking
export const analytics = {
  // Track retreat-specific events
  trackRetreatEvent: (eventName, properties = {}) => {
    if (typeof window === 'undefined') return;
    
    // Google Analytics
    if (window.gtag) {
      window.gtag('event', eventName, {
        event_category: 'Retreat',
        event_label: properties.retreatName || 'Unknown',
        value: properties.value || 0,
        custom_parameter_1: properties.retreatName,
        custom_parameter_2: properties.userJourneyStage || 'unknown'
      });
    }
    
    // Mixpanel
    if (window.mixpanel) {
      window.mixpanel.track(eventName, {
        ...properties,
        category: 'retreat',
        timestamp: new Date().toISOString()
      });
    }
    
    // Hotjar
    if (window.hj) {
      window.hj('event', eventName);
    }
  },

  // Track booking funnel steps
  trackBookingStep: (step, retreatName, additionalData = {}) => {
    const eventData = {
      retreat_name: retreatName,
      booking_step: step,
      ...additionalData
    };

    // Google Analytics Enhanced Ecommerce
    if (window.gtag) {
      window.gtag('event', 'begin_checkout', {
        currency: 'PLN',
        value: additionalData.value || 0,
        items: [{
          item_id: retreatName.toLowerCase().replace(/\s+/g, '_'),
          item_name: retreatName,
          category: 'Retreat',
          quantity: 1,
          price: additionalData.price || 0
        }]
      });
    }

    // Track the step
    analytics.trackRetreatEvent('booking_step', eventData);
  },

  // Track form interactions
  trackFormInteraction: (formName, action, fieldName = null) => {
    const eventData = {
      form_name: formName,
      action: action,
      field_name: fieldName
    };

    if (window.gtag) {
      window.gtag('event', 'form_interaction', {
        event_category: 'Form',
        event_label: formName,
        custom_parameter_1: action,
        custom_parameter_2: fieldName || 'unknown'
      });
    }

    if (window.mixpanel) {
      window.mixpanel.track('Form Interaction', eventData);
    }
  },

  // Track page engagement
  trackPageEngagement: (engagementType, duration = null) => {
    const eventData = {
      engagement_type: engagementType,
      duration: duration,
      page: window.location.pathname
    };

    if (window.gtag) {
      window.gtag('event', 'engagement', {
        event_category: 'User Engagement',
        event_label: engagementType,
        value: duration || 0
      });
    }

    if (window.mixpanel) {
      window.mixpanel.track('Page Engagement', eventData);
    }
  },

  // Track retreat interest indicators
  trackRetreatInterest: (retreatName, interestType) => {
    const eventData = {
      retreat_name: retreatName,
      interest_type: interestType
    };

    // Google Analytics
    if (window.gtag) {
      window.gtag('event', 'view_item', {
        currency: 'PLN',
        value: 1,
        items: [{
          item_id: retreatName.toLowerCase().replace(/\s+/g, '_'),
          item_name: retreatName,
          category: 'Retreat',
          quantity: 1
        }]
      });
    }

    analytics.trackRetreatEvent('retreat_interest', eventData);

    // Hotjar specific tracking
    if (window.hjTrackRetreatInteraction) {
      window.hjTrackRetreatInteraction(retreatName, interestType);
    }
  },

  // Track conversion events
  trackConversion: (conversionType, value = 0, retreatName = null) => {
    const eventData = {
      conversion_type: conversionType,
      value: value,
      retreat_name: retreatName
    };

    // Google Analytics conversion
    if (window.gtag) {
      window.gtag('event', 'conversion', {
        event_category: 'Conversion',
        event_label: conversionType,
        value: value,
        currency: 'PLN'
      });

      // If it's a purchase
      if (conversionType === 'purchase' && retreatName) {
        window.gtag('event', 'purchase', {
          transaction_id: Date.now().toString(),
          value: value,
          currency: 'PLN',
          items: [{
            item_id: retreatName.toLowerCase().replace(/\s+/g, '_'),
            item_name: retreatName,
            category: 'Retreat',
            quantity: 1,
            price: value
          }]
        });
      }
    }

    // Facebook Pixel
    if (window.fbq) {
      if (conversionType === 'purchase') {
        window.fbq('track', 'Purchase', {
          value: value,
          currency: 'PLN',
          content_name: retreatName,
          content_category: 'Retreat'
        });
      } else {
        window.fbq('track', 'Lead', eventData);
      }
    }

    analytics.trackRetreatEvent('conversion', eventData);
  },

  // Track user identification
  identifyUser: (userId, userProperties = {}) => {
    // Google Analytics
    if (window.gtag) {
      window.gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID, {
        user_id: userId
      });
    }

    // Mixpanel
    if (window.mixpanel) {
      window.mixpanel.identify(userId);
      window.mixpanel.people.set({
        ...userProperties,
        $last_seen: new Date()
      });
    }

    // Sentry
    if (window.Sentry) {
      window.Sentry.setUser({
        id: userId,
        ...userProperties
      });
    }

    // Hotjar
    if (window.hj) {
      window.hj('identify', userId, userProperties);
    }
  }
};

// Export consent management utilities
export const consentManager = {
  // Grant analytics consent
  grantConsent: () => {
    localStorage.setItem('analytics-consent', 'true');
    
    // Update Google Analytics consent
    if (window.gtag) {
      window.gtag('consent', 'update', {
        analytics_storage: 'granted',
        ad_storage: 'granted'
      });
    }
    
    // Reload page to initialize analytics
    window.location.reload();
  },

  // Revoke analytics consent
  revokeConsent: () => {
    localStorage.setItem('analytics-consent', 'false');
    
    // Update Google Analytics consent
    if (window.gtag) {
      window.gtag('consent', 'update', {
        analytics_storage: 'denied',
        ad_storage: 'denied'
      });
    }
    
    // Clear existing data
    localStorage.removeItem('_ga');
    localStorage.removeItem('_ga_' + process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID?.replace('G-', ''));
    
    // Reload page to disable analytics
    window.location.reload();
  },

  // Check current consent status
  hasConsent: () => {
    const consent = localStorage.getItem('analytics-consent');
    return consent !== 'false';
  }
};
