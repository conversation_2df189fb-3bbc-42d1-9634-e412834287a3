
export const metadata = {
  title: '[slug] - BAKASANA',
  description: 'Strona [slug] - BAKASANA retreaty jogi na Bali i Sri Lanka z Julią Jakubowicz.',
  openGraph: {
    title: '[slug] - BAKASANA',
    description: 'Strona [slug] - BAKASANA retreaty jogi na Bali i Sri Lanka z Julią Jakubowicz.',
    images: ['/images/og-image.jpg'],
  },
  twitter: {
    card: 'summary_large_image',
    title: '[slug] - BAKASANA',
    description: 'Strona [slug] - BAKASANA retreaty jogi na Bali i Sri Lanka z Julią Jakubowicz.',
  }
};

// src/app/program/[slug]/page.jsx
export default function ProgramPage({ params }) {
  // params.slug będzie zawierał nazwę wycieczki z URL
  // np. jeśli URL to /program/wycieczka-bali
  // to params.slug będzie równy "wycieczka-bali"

  return (
    <div>
      <h1>Program wycieczki: {params.slug}</h1>
      {/* Tu dodaj resztę treści strony */}
    </div>
  );
}
