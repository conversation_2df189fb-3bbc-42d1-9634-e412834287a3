
import Image from 'next/image';
import Link from 'next/link';

import { Globe, Plane, Shield  } from 'lucide-react';

import { Badge  } from '@/components/ui/badge';
import { Button  } from '@/components/ui/button';
import { Card, CardContent  } from '@/components/ui/card';
import { HeroTitle, SectionTitle, CardTitle, BodyText  } from '@/components/ui/UnifiedTypography';
import { Icon   } from '@/components/ui/IconSystem';

export const metadata = {
  title: 'Yoga Retreat z Polski 2025 - Najlepsze Oferty Bali & Sri Lanka | BAKASANA',
  description: '🇵🇱 Yoga retreat z Polski 2025 - Bali i Sri Lanka z polską opieką! Certyfikowana instruktorka, małe grupy, all-inclusive. ✈️ Loty z Warszawy, Krakowa, Gdańska. 4.9/5 ⭐',
  keywords: 'yoga retreat polska, yoga retreat z polski, retreat jogi z polski, bali z polski, sri lanka z polski, yoga wakacje polska, polskie retreaty jogi',
  openGraph: {
    title: 'Yoga Retreat z Polski 2025 - <PERSON>jle<PERSON>ze Oferty | BAKASANA',
    description: '🇵🇱 Yoga retreat z Polski - Bali i Sri Lanka z polską opieką! Certyfikowana instruktorka, all-inclusive. 4.9/5 ⭐',
    images: ['/images/og/yoga-retreat-z-polski-2025.jpg'],
  },
  alternates: {
    canonical: 'https://bakasana-travel.blog/yoga-retreat-z-polski',
  },
};

const YogaRetreatZPolski = () => {
  const polishAdvantages = [
    {
      icon: <Globe className="w-5 h-5 text-charcoal" />,
      title: "Polska instruktorka",
      description: "Julia Jakubowicz - rodzima instruktorka jogi"
    },
    {
      icon: <Icon name="users" size="md" color="primary" />,
      title: "Polska grupa",
      description: "Poznaj nowych przyjaciół z Polski"
    },
    {
      icon: <Shield className="w-5 h-5 text-charcoal" />,
      title: "Bezpieczeństwo",
      description: "Pełna opieka przez cały pobyt"
    },
    {
      icon: <Icon name="heart" size="md" color="primary" />,
      title: "Bez barier językowych",
      description: "Wszystko w języku polskim"
    }
  ];

  const destinations = [
    {
      name: "Bali",
      duration: "7-14 dni",
      price: "3400 PLN",
      highlights: [
        "Ubud - tarasy ryżowe",
        "Gili Air - rajskie plaże", 
        "Świątynie hinduistyczne",
        "Ayurveda masaże",
        "Warung food tours"
      ],
      image: "/images/destinations/bali-retreat-polish.webp",
      badge: "Najpopularniejszy",
      badgeColor: "bg-charcoal"
    },
    {
      name: "Sri Lanka",
      duration: "10-14 dni",
      price: "3800 PLN",
      highlights: [
        "Sigiriya - Lwia Skała",
        "Kandy - górskie miasta",
        "Ella - plantacje herbaty",
        "Ayurveda authentic",
        "Południowe plaże"
      ],
      image: "/images/destinations/srilanka-retreat-polish.webp",
      badge: "Dla odkrywców",
      badgeColor: "bg-terra"
    }
  ];

  const includedServices = [
    {
      category: "Transport",
      items: [
        "Assistance przy locie z Polski",
        "Odbiór z lotniska",
        "Transport lokalny",
        "Pomoc z przesiadkami"
      ]
    },
    {
      category: "Zakwaterowanie",
      items: [
        "Hotele 4-5 gwiazdek",
        "Pokoje klimatyzowane",
        "WiFi w całym hotelu",
        "Śniadania w cenie"
      ]
    },
    {
      category: "Joga & Wellness",
      items: [
        "Daily joga w języku polskim",
        "Medytacja mindfulness",
        "Ayurveda masaże",
        "Breathing workshops"
      ]
    },
    {
      category: "Kultura & Zwiedzanie",
      items: [
        "Przewodnik po świątyniach",
        "Local food experiences",
        "Cultural immersion",
        "Photography workshops"
      ]
    }
  ];

  const polishCities = [
    { city: "Warszawa", airport: "WAW", price: "Od 2800 PLN" },
    { city: "Kraków", airport: "KRK", price: "Od 2900 PLN" },
    { city: "Gdańsk", airport: "GDN", price: "Od 3100 PLN" },
    { city: "Wrocław", airport: "WRO", price: "Od 3000 PLN" },
    { city: "Katowice", airport: "KTW", price: "Od 2950 PLN" },
    { city: "Poznań", airport: "POZ", price: "Od 3050 PLN" }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-section bg-gradient-to-br from-charcoal/10 to-golden/10">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/destinations/polish-group-yoga.webp" alt="Image"
            alt="Yoga retreat z Polski - grupa podczas praktyki"
            fill
            className="object-cover opacity-20"
            priority
          />
        </div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-container-sm">
          <div className="text-center">
            <Badge className="mb-md bg-white/90 text-charcoal border-charcoal/30 px-container-sm py-2">
              🇵🇱 Yoga Retreat z Polski 2025
            </Badge>
            
            <h1 className="text-4xl md:text-6xl font-bold text-charcoal mb-md /* TODO: Replace with HeroTitle */ /* TODO: Replace with HeroTitle */" /* TODO: Replace with HeroTitle */>
              Najlepsze Yoga Retreat <br />
              <span className="text-charcoal">z Polski</span>
            </h1>
            
            <p className="text-xl text-wood mb-lg max-w-3xl mx-auto /* TODO: Replace with CardTitle */">
              Odkryj magię jogi na Bali i Sri Lanka z polską opieką! 
              Certyfikowana instruktorka Julia Jakubowicz, małe grupy, 
              all-inclusive pakiety. Loty z głównych miast Polski.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-sm justify-center mb-lg">
              <Button 
                size="lg" 
                className="bg-charcoal hover:bg-charcoal/90 text-lg px-hero-padding py-6"
                asChild
              >
                <Link href="/rezerwacja">
                  Zarezerwuj z Polski
                  <Plane className="w-5 h-5 ml-2" />
                </Link>
              </Button>
              
              <Button 
                size="lg" 
                variant="outline" 
                className="border-charcoal text-charcoal hover:bg-charcoal/10 text-lg px-hero-padding py-6"
                asChild
              >
                <Link href="/program">
                  Zobacz Programy
                </Link>
              </Button>
            </div>
            
            <div className="flex flex-wrap items-center justify-center gap-md text-wood">
              <div className="flex items-center gap-2">
                <Icon name="star" size="md" color="accent" />
                <span className="font-semibold">4.9/5</span>
                <span>(127 polskich uczestników)</span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="w-5 h-5 text-charcoal" />
                <span>Pełna opieka</span>
              </div>
              <div className="flex items-center gap-2">
                <Globe className="w-5 h-5 text-charcoal" />
                <span>Język polski</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Poland */}
      <section className="py-section-md bg-sanctuary">
        <div className="max-w-7xl mx-auto px-container-sm">
          <div className="text-center mb-xl">
            <h2 className="text-3xl font-bold text-charcoal mb-sm /* TODO: Replace with SectionTitle */" /* TODO: Replace with SectionTitle */>
              Dlaczego wybierać yoga retreat z Polski?
            </h2>
            <p className="text-wood max-w-2xl mx-auto">
              Podróżuj z rodakami, bez barier językowych, z pełną opieką 
              i wsparciem na każdym kroku egzotycznej przygody.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-md">
            {polishAdvantages.map((advantage, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-charcoal/20">
                <CardContent className="p-6 text-center">
                  <div className="w-12 h-12 bg-charcoal/10 flex items-center justify-center mx-auto mb-sm">
                    {advantage.icon}
                  </div>
                  <h3 className="font-semibold text-charcoal mb-2">{advantage.title}</h3>
                  <p className="text-sm text-wood">{advantage.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Destinations */}
      <section className="py-section-md">
        <div className="max-w-7xl mx-auto px-container-sm">
          <div className="text-center mb-xl">
            <h2 className="text-3xl font-bold text-charcoal mb-sm" /* TODO: Replace with SectionTitle */>
              Wybierz swój kierunek
            </h2>
            <p className="text-wood max-w-2xl mx-auto">
              Dwa wyjątkowe miejsca na yoga retreat z Polski. 
              Każdy kierunek oferuje unikalne doświadczenia.
            </p>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-lg">
            {destinations.map((destination, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-charcoal/20 overflow-hidden">
                <div className="relative h-64">
                  <Image
                    src={destination.image}
                    alt={`Yoga retreat ${destination.name} z Polski`}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute top-4 left-4">
                    <Badge className={`${destination.badgeColor} text-white`}>
                      {destination.badge}
                    </Badge>
                  </div>
                </div>
                
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-sm">
                    <SectionTitle level={3}>{destination.name}</SectionTitle>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-charcoal /* TODO: Replace with SectionTitle */">{destination.price}</div>
                      <div className="text-sm text-wood">{destination.duration}</div>
                    </div>
                  </div>
                  
                  <div className="space-y-3 mb-md">
                    {destination.highlights.map((highlight, hIndex) => (
                      <div key={hIndex} className="flex items-center gap-2">
                        <Icon name="check" size="sm" color="primary" />
                        <span className="text-wood text-sm">{highlight}</span>
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex gap-3">
                    <Button 
                      className="flex-1 bg-charcoal hover:bg-charcoal/90"
                      asChild
                    >
                      <Link href={`/program?destination=${destination.name.toLowerCase()}`}>
                        Szczegóły
                      </Link>
                    </Button>
                    <Button 
                      variant="outline" 
                      className="flex-1 border-charcoal text-charcoal hover:bg-charcoal/10"
                      asChild
                    >
                      <Link href="/rezerwacja">
                        Rezerwuj
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Flight Options */}
      <section className="py-section-md bg-sanctuary">
        <div className="max-w-7xl mx-auto px-container-sm">
          <div className="text-center mb-xl">
            <h2 className="text-3xl font-bold text-charcoal mb-sm" /* TODO: Replace with SectionTitle */>
              Loty z Polski - wszystkie główne miasta
            </h2>
            <p className="text-wood max-w-2xl mx-auto">
              Organizujemy loty z największych miast Polski. 
              Pomoc w organizacji, grupowe przesiadki, wsparcie 24/7.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-md">
            {polishCities.map((city, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-charcoal/20">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-sm">
                    <div>
                      <h3 className="font-semibold text-charcoal text-lg">{city.city}</h3>
                      <p className="text-wood text-sm">Lotnisko {city.airport}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-charcoal font-bold">{city.price}</div>
                      <div className="text-xs text-wood">+ retreat</div>
                    </div>
                  </div>
                  
                  <div className="space-y-2 mb-sm">
                    <div className="flex items-center gap-2">
                      <Plane className="w-4 h-4 text-charcoal" />
                      <span className="text-sm text-wood">Loty grupowe</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Shield className="w-4 h-4 text-charcoal" />
                      <span className="text-sm text-wood">Assistance</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Icon name="users" size="sm" color="primary" />
                      <span className="text-sm text-wood">Pomoc z przesiadkami</span>
                    </div>
                  </div>
                  
                  <Button 
                    size="sm" 
                    className="w-full bg-charcoal hover:bg-charcoal/90"
                    asChild
                  >
                    <Link href={`/rezerwacja?city=${city.city.toLowerCase()}`}>
                      Sprawdź terminy
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* What's Included */}
      <section className="py-section-md">
        <div className="max-w-7xl mx-auto px-container-sm">
          <div className="text-center mb-xl">
            <h2 className="text-3xl font-bold text-charcoal mb-sm" /* TODO: Replace with SectionTitle */>
              Co zawiera pakiet retreat z Polski?
            </h2>
            <p className="text-wood max-w-2xl mx-auto">
              Kompleksowa opieka od momentu wyjazdu z Polski 
              do bezpiecznego powrotu do domu.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-md">
            {includedServices.map((service, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-charcoal/20">
                <CardContent className="p-6">
                  <h3 className="font-semibold text-charcoal mb-sm text-center">
                    {service.category}
                  </h3>
                  <div className="space-y-3">
                    {service.items.map((item, itemIndex) => (
                      <div key={itemIndex} className="flex items-start gap-2">
                        <Icon name="check" size="sm" color="primary" />
                        <span className="text-wood text-sm">{item}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Polish Community */}
      <section className="py-section-md bg-charcoal/5">
        <div className="max-w-7xl mx-auto px-container-sm">
          <div className="grid lg:grid-cols-2 gap-xl items-center">
            <div>
              <h2 className="text-3xl font-bold text-charcoal mb-md" /* TODO: Replace with SectionTitle */>
                Dołącz do polskiej społeczności BAKASANA
              </h2>
              
              <div className="space-y-sm mb-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-charcoal/10 flex items-center justify-center">
                    <Icon name="users" size="sm" color="primary" />
                  </div>
                  <span className="text-wood">127 Polaków już doświadczyło transformacji</span>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-charcoal/10 flex items-center justify-center">
                    <Icon name="star" size="sm" color="primary" />
                  </div>
                  <span className="text-wood">4.9/5 średnia ocena od polskich uczestników</span>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-charcoal/10 flex items-center justify-center">
                    <Icon name="heart" size="sm" color="primary" />
                  </div>
                  <span className="text-wood">Dożywotnie przyjaźnie i wspólne wartości</span>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-charcoal/10 flex items-center justify-center">
                    <Globe className="w-4 h-4 text-charcoal" />
                  </div>
                  <span className="text-wood">Polskie wsparcie na każdym kroku</span>
                </div>
              </div>
              
              <div className="bg-white/80 backdrop-blur-sm p-6 border border-charcoal/20">
                <h3 className="font-semibold text-charcoal mb-3">Specjalne ceny dla Polaków:</h3>
                <div className="flex items-center gap-sm mb-3">
                  <span className="text-3xl font-bold text-charcoal">Od 3400 PLN</span>
                  <Badge className="bg-terra/20 text-terra">
                    Early Bird -300 PLN
                  </Badge>
                </div>
                <p className="text-sm text-wood">
                  Ceny all-inclusive z lotem z Polski. Możliwość rozłożenia płatności na raty.
                </p>
              </div>
            </div>
            
            <div className="relative">
              <div className="grid grid-cols-2 gap-sm">
                <div className="space-y-sm">
                  <div className="relative aspect-square overflow-hidden">
                    <Image
                      src="/images/community/polish-group-bali.webp" alt="Image"
                      alt="Polska grupa podczas retreatu na Bali"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="relative aspect-square overflow-hidden">
                    <Image
                      src="/images/community/polish-group-dinner.webp" alt="Image"
                      alt="Polscy uczestnicy podczas wspólnego posiłku"
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>
                <div className="space-y-sm mt-lg">
                  <div className="relative aspect-square overflow-hidden">
                    <Image
                      src="/images/community/polish-group-yoga.webp" alt="Image"
                      alt="Polska grupa podczas praktyki jogi"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="relative aspect-square overflow-hidden">
                    <Image
                      src="/images/community/polish-group-temple.webp" alt="Image"
                      alt="Polscy uczestnicy w świątyni"
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-section-md bg-gradient-to-r from-charcoal/10 to-golden/10">
        <div className="max-w-4xl mx-auto px-container-sm text-center">
          <h2 className="text-3xl font-bold text-charcoal mb-sm" /* TODO: Replace with SectionTitle */>
            Gotowy na yoga retreat z Polski?
          </h2>
          <p className="text-wood mb-lg max-w-2xl mx-auto">
            Dołącz do nas na wyjątkowym yoga retreat z polską opieką. 
            Terminy 2025 dostępne - ograniczona liczba miejsc!
          </p>
          
          <div className="flex flex-col sm:flex-row gap-sm justify-center mb-lg">
            <Button 
              size="lg" 
              className="bg-charcoal hover:bg-charcoal/90 text-lg px-hero-padding py-6"
              asChild
            >
              <Link href="/rezerwacja">
                Zarezerwuj z Polski
                <Plane className="w-5 h-5 ml-2" />
              </Link>
            </Button>
            
            <Button 
              size="lg" 
              variant="outline" 
              className="border-charcoal text-charcoal hover:bg-charcoal/10 text-lg px-hero-padding py-6"
              asChild
            >
              <Link href="/kontakt">
                Zadaj Pytanie
              </Link>
            </Button>
          </div>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-md text-wood">
            <div className="flex items-center gap-2">
              <Icon name="phone" size="sm" color="primary" />
              <span>+48 666 777 888</span>
            </div>
            <div className="flex items-center gap-2">
              <Icon name="mail" size="sm" color="primary" />
              <span><EMAIL></span>
            </div>
            <div className="flex items-center gap-2">
              <Icon name="instagram" size="sm" color="primary" />
              <span>@fly_with_bakasana</span>
            </div>
          </div>
        </div>
      </section>

      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "TravelAgency",
            "name": "BAKASANA - Yoga Retreat z Polski",
            "description": "Najlepsze yoga retreat z Polski na Bali i Sri Lanka z certyfikowaną polską instruktorką",
            "url": "https://bakasana-travel.blog/yoga-retreat-z-polski",
            "image": "https://bakasana-travel.blog/images/og/yoga-retreat-z-polski-2025.jpg",
            "address": {
              "@type": "PostalAddress",
              "addressCountry": "PL"
            },
            "offers": [
              {
                "@type": "Offer",
                "name": "Yoga Retreat Bali z Polski",
                "price": "3400",
                "priceCurrency": "PLN",
                "availability": "https://schema.org/InStock"
              },
              {
                "@type": "Offer",
                "name": "Yoga Retreat Sri Lanka z Polski", 
                "price": "3800",
                "priceCurrency": "PLN",
                "availability": "https://schema.org/InStock"
              }
            ],
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.9",
              "reviewCount": "127"
            }
          })
        }}
      />
    </div>
  );
};

export default YogaRetreatZPolski;

