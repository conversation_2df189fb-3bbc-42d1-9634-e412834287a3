{"name": "image-dimensions", "version": "2.3.0", "description": "Get the dimensions of an image", "license": "MIT", "repository": "sindresorhus/image-dimensions", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "bin": "./cli.js", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsc index.d.ts"}, "files": ["index.js", "index.d.ts", "utilities.js", "types", "cli.js"], "keywords": ["image", "dimensions", "width", "height", "size", "resolution", "metadata", "uint8array", "buffer", "data", "bytes", "binary", "stream", "cli", "cli-app", "png", "jpeg", "jpg", "gif", "webp", "avif"], "devDependencies": {"ava": "^6.0.1", "typescript": "^5.3.3", "xo": "^0.56.0"}}