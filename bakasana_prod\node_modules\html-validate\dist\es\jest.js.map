{"version": 3, "file": "jest.js", "sources": ["../../src/jest/jest.ts"], "sourcesContent": ["import \"./augmentation\";\n\nimport {\n\ttoBeValid,\n\ttoBeInvalid,\n\ttoHTMLValidate,\n\ttoHaveError,\n\ttoHaveErrors,\n\ttoMatchCodeframe,\n\ttoMatchInlineCodeframe,\n} from \"./matchers\";\nimport { diff } from \"./utils\";\n\nexpect.extend({\n\ttoBeValid: toBeValid(),\n\ttoBeInvalid: toBeInvalid(),\n\ttoHTMLValidate: toHTMLValidate(expect, diff),\n\ttoHaveError: toHaveError(expect, diff),\n\ttoHaveErrors: toHaveErrors(expect, diff),\n\ttoMatchCodeframe: toMatchCodeframe(),\n\ttoMatchInlineCodeframe: toMatchInlineCodeframe(),\n});\n"], "names": ["toBeValid", "toBeInvalid", "toHTMLValidate", "toHaveError", "toHaveErrors", "toMatchCodeframe", "toMatchInlineCodeframe"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAaA,MAAA,CAAO,MAAA,CAAO;AAAA,EACb,WAAWA,eAAA,EAAU;AAAA,EACrB,aAAaC,eAAA,EAAY;AAAA,EACzB,cAAA,EAAgBC,eAAA,CAAe,MAAA,EAAQ,IAAI,CAAA;AAAA,EAC3C,WAAA,EAAaC,eAAA,CAAY,MAAA,EAAQ,IAAI,CAAA;AAAA,EACrC,YAAA,EAAcC,eAAA,CAAa,MAAA,EAAQ,IAAI,CAAA;AAAA,EACvC,kBAAkBC,eAAA,EAAiB;AAAA,EACnC,wBAAwBC,aAAA;AACzB,CAAC,CAAA"}