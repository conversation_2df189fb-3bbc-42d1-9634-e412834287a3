html body nav .hidden.md\\:flex,html body nav div.hidden.md\\:flex{display:flex;visibility:visible;opacity:1}html body nav .hidden.md\\:block,html body nav div.hidden.md\\:block{display:block;visibility:visible;opacity:1}@media screen and (min-width:1024px){nav [class*="hidden"][class*="md:flex"]{display:flex !important;visibility:visible !important;opacity:1 !important}nav [class*="hidden"][class*="md:block"]{display:block !important;visibility:visible !important;opacity:1 !important}nav [class*="md:hidden"]{display:none !important}nav button[aria-label="Toggle menu"]{display:none !important}}@media screen and (max-width:1023px){nav [class*="md:hidden"]{display:block !important;visibility:visible !important;opacity:1 !important}nav [class*="hidden"][class*="md:flex"],nav [class*="hidden"][class*="md:block"]{display:none !important}nav button[aria-label="Toggle menu"]{display:block !important;visibility:visible !important;opacity:1 !important}}.navbar-logo,nav .navbar-logo,nav a.navbar-logo{display:block !important;visibility:visible !important;opacity:1 !important}nav *{box-sizing:border-box}nav .hidden.md\\:flex::before{content:"Desktop Menu Should Be Visible";position:absolute;top:-20px;left:0;font-size:10px;color:red;background:yellow;padding:2px;z-index:9999;display:none}@media (min-width:1024px){nav .hidden.md\\:flex::before{display:block}}