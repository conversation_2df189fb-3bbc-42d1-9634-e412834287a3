{"version": 3, "file": "vitest.js", "sources": ["../../src/vitest/vitest.ts"], "sourcesContent": ["import \"./augmentation\";\n\nimport { expect } from \"vitest\";\nimport {\n\ttoBeValid,\n\ttoBeInvalid,\n\ttoHTMLValidate,\n\ttoHaveError,\n\ttoHaveErrors,\n} from \"../jest/matchers\";\n\nexpect.extend({\n\ttoBeValid: toBeValid(),\n\ttoBeInvalid: toBeInvalid(),\n\ttoHTMLValidate: toHTMLValidate(expect, undefined),\n\ttoHaveError: toHaveError(expect, undefined),\n\ttoHaveErrors: toHaveErrors(expect, undefined),\n});\n"], "names": ["expect", "toBeValid", "toBeInvalid", "toHTMLValidate", "toHaveError", "toHaveErrors"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAWAA,aAAA,CAAO,MAAA,CAAO;AAAA,EACb,WAAWC,wBAAA,EAAU;AAAA,EACrB,aAAaC,wBAAA,EAAY;AAAA,EACzB,cAAA,EAAgBC,wBAAA,CAAeH,aAAA,EAAQ,MAAS,CAAA;AAAA,EAChD,WAAA,EAAaI,wBAAA,CAAYJ,aAAA,EAAQ,MAAS,CAAA;AAAA,EAC1C,YAAA,EAAcK,sBAAA,CAAaL,aAAA,EAAQ,MAAS;AAC7C,CAAC,CAAA;;"}