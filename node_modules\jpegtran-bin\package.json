{"name": "jpegtran-bin", "version": "5.0.2", "description": "jpegtran (part of libjpeg-turbo) bin-wrapper that makes it seamlessly available as a local dependency", "license": "MIT", "repository": "imagemin/jpegtran-bin", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, {"name": "<PERSON><PERSON><PERSON>", "url": "github.com/shinnn"}], "bin": {"jpegtran": "cli.js"}, "engines": {"node": ">=10"}, "scripts": {"postinstall": "node lib/install.js", "test": "xo && ava --timeout=120s"}, "files": ["index.js", "cli.js", "lib", "test", "vendor/source"], "keywords": ["imagemin", "compress", "image", "img", "jpeg", "jpg", "minify", "optimize", "jpegtran"], "dependencies": {"bin-build": "^3.0.0", "bin-wrapper": "^4.0.0", "logalot": "^2.0.0"}, "devDependencies": {"ava": "^3.8.0", "bin-check": "^4.0.1", "compare-size": "^3.0.0", "execa": "^4.0.0", "tempy": "^0.5.0", "xo": "^0.30.0"}}