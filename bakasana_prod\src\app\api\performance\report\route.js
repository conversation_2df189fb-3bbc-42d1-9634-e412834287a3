import { NextResponse } from 'next/server';

// External monitoring service configurations
const GTMETRIX_API_KEY = process.env.GTMETRIX_API_KEY;
const GTMETRIX_API_URL = 'https://gtmetrix.com/api/2.0';

const PINGDOM_API_KEY = process.env.PINGDOM_API_KEY;
const PINGDOM_API_URL = 'https://api.pingdom.com/api/3.1';

const UPTIMEROBOT_API_KEY = process.env.UPTIMEROBOT_API_KEY;
const UPTIMEROBOT_API_URL = 'https://api.uptimerobot.com/v2';

// Rate limiting to prevent spam
const rateLimitMap = new Map();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 10;

function checkRateLimit(ip) {
  const now = Date.now();
  const windowStart = now - RATE_LIMIT_WINDOW;
  
  if (!rateLimitMap.has(ip)) {
    rateLimitMap.set(ip, []);
  }
  
  const requests = rateLimitMap.get(ip);
  const recentRequests = requests.filter(time => time > windowStart);
  
  if (recentRequests.length >= RATE_LIMIT_MAX_REQUESTS) {
    return false;
  }
  
  recentRequests.push(now);
  rateLimitMap.set(ip, recentRequests);
  return true;
}

// GTmetrix API integration
async function sendToGTmetrix(performanceData) {
  if (!GTMETRIX_API_KEY) return null;

  try {
    const response = await fetch(`${GTMETRIX_API_URL}/reports`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${Buffer.from(`${GTMETRIX_API_KEY}:`).toString('base64')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: `https://bakasana-travel.blog${performanceData.page}`,
        location: 'vancouver',
        browser: 'chrome',
        options: {
          performance_report: 1,
          video: 1,
          retention_period: 30
        }
      }),
    });

    if (!response.ok) {
      throw new Error(`GTmetrix API error: ${response.status}`);
    }

    const result = await response.json();
    console.log('GTmetrix report initiated:', result.report_id);
    return result;
  } catch (error) {
    console.error('GTmetrix API error:', error);
    return null;
  }
}

// Pingdom API integration
async function sendToPingdom(performanceData) {
  if (!PINGDOM_API_KEY) return null;

  try {
    // Get existing checks first
    const checksResponse = await fetch(`${PINGDOM_API_URL}/checks`, {
      headers: {
        'Authorization': `Bearer ${PINGDOM_API_KEY}`,
      },
    });

    if (!checksResponse.ok) {
      throw new Error(`Pingdom API error: ${checksResponse.status}`);
    }

    const checks = await checksResponse.json();
    
    // Find or create check for this page
    const pageUrl = `https://bakasana-travel.blog${performanceData.page}`;
    let existingCheck = checks.checks?.find(check => check.hostname === pageUrl);

    if (!existingCheck) {
      // Create new check
      const createResponse = await fetch(`${PINGDOM_API_URL}/checks`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${PINGDOM_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: `BAKASANA - ${performanceData.page}`,
          host: 'bakasana-travel.blog',
          type: 'http',
          url: pageUrl,
          resolution: 5, // Check every 5 minutes
          tags: ['bakasana', 'performance', 'retreat-website']
        }),
      });

      if (createResponse.ok) {
        const newCheck = await createResponse.json();
        console.log('Pingdom check created:', newCheck.check.id);
        return newCheck;
      }
    }

    return existingCheck;
  } catch (error) {
    console.error('Pingdom API error:', error);
    return null;
  }
}

// UptimeRobot API integration
async function sendToUptimeRobot(performanceData) {
  if (!UPTIMEROBOT_API_KEY) return null;

  try {
    // Get existing monitors
    const monitorsResponse = await fetch(UPTIMEROBOT_API_URL + '/getMonitors', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        api_key: UPTIMEROBOT_API_KEY,
        format: 'json',
        search: 'bakasana-travel.blog'
      }),
    });

    if (!monitorsResponse.ok) {
      throw new Error(`UptimeRobot API error: ${monitorsResponse.status}`);
    }

    const monitors = await monitorsResponse.json();
    
    // Check if monitor exists for this domain
    const pageUrl = `https://bakasana-travel.blog${performanceData.page}`;
    let existingMonitor = monitors.monitors?.find(monitor => 
      monitor.url === pageUrl || monitor.url === 'https://bakasana-travel.blog'
    );

    if (!existingMonitor) {
      // Create new monitor
      const createResponse = await fetch(UPTIMEROBOT_API_URL + '/newMonitor', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          api_key: UPTIMEROBOT_API_KEY,
          format: 'json',
          type: 1, // HTTP(s)
          url: pageUrl,
          friendly_name: `BAKASANA ${performanceData.page}`,
          interval: 300, // 5 minutes
          keyword_type: 1,
          keyword_value: 'BAKASANA'
        }),
      });

      if (createResponse.ok) {
        const newMonitor = await createResponse.json();
        console.log('UptimeRobot monitor created:', newMonitor.monitor.id);
        return newMonitor;
      }
    }

    return existingMonitor;
  } catch (error) {
    console.error('UptimeRobot API error:', error);
    return null;
  }
}

// Store performance data locally for analysis
async function storePerformanceData(data) {
  // In a real application, you would store this in a database
  // For now, we'll just log it and could write to a file or send to a logging service
  
  const performanceLog = {
    timestamp: new Date().toISOString(),
    page: data.page,
    metrics: {
      lcp: data.lcp,
      fid: data.fid,
      cls: data.cls,
      ttfb: data.ttfb,
      memory: data.memory
    },
    userAgent: data.userAgent,
    connection: data.connection,
    sessionId: data.sessionId || 'anonymous'
  };

  console.log('Performance data stored:', performanceLog);
  
  // You could send this to a logging service like:
  // - Datadog
  // - New Relic
  // - LogRocket
  // - Custom analytics database
  
  return performanceLog;
}

export async function POST(request) {
  try {
    // Get client IP for rate limiting
    const forwarded = request.headers.get('x-forwarded-for');
    const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown';

    // Check rate limit
    if (!checkRateLimit(ip)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      );
    }

    const performanceData = await request.json();

    // Validate required data
    if (!performanceData.page) {
      return NextResponse.json(
        { error: 'Page path is required' },
        { status: 400 }
      );
    }

    // Store performance data locally
    const storedData = await storePerformanceData(performanceData);

    // Send to external monitoring services (in parallel)
    const [gtmetrixResult, pingdomResult, uptimeRobotResult] = await Promise.allSettled([
      sendToGTmetrix(performanceData),
      sendToPingdom(performanceData),
      sendToUptimeRobot(performanceData)
    ]);

    // Prepare response
    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      stored: !!storedData,
      external_services: {
        gtmetrix: gtmetrixResult.status === 'fulfilled' ? 'success' : 'failed',
        pingdom: pingdomResult.status === 'fulfilled' ? 'success' : 'failed',
        uptimerobot: uptimeRobotResult.status === 'fulfilled' ? 'success' : 'failed'
      }
    };

    // Log any failures
    if (gtmetrixResult.status === 'rejected') {
      console.error('GTmetrix failed:', gtmetrixResult.reason);
    }
    if (pingdomResult.status === 'rejected') {
      console.error('Pingdom failed:', pingdomResult.reason);
    }
    if (uptimeRobotResult.status === 'rejected') {
      console.error('UptimeRobot failed:', uptimeRobotResult.reason);
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Performance report API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
      },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve performance data
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page');
    const days = parseInt(searchParams.get('days')) || 7;

    // In a real application, you would query your database here
    // For now, return a mock response
    const mockData = {
      page: page || 'all',
      period: `${days} days`,
      metrics: {
        average_lcp: 1200,
        average_fid: 50,
        average_cls: 0.05,
        average_ttfb: 400,
        uptime_percentage: 99.9,
        total_requests: 1500
      },
      external_services: {
        gtmetrix: {
          status: 'active',
          last_check: new Date().toISOString(),
          performance_score: 95
        },
        pingdom: {
          status: 'active',
          response_time: 250,
          uptime: 99.9
        },
        uptimerobot: {
          status: 'active',
          uptime: 99.95,
          response_time: 280
        }
      }
    };

    return NextResponse.json(mockData);

  } catch (error) {
    console.error('Performance data retrieval error:', error);
    
    return NextResponse.json(
      { error: 'Failed to retrieve performance data' },
      { status: 500 }
    );
  }
}
