import { ErrorObject } from 'ajv';
import { SchemaObject } from 'ajv';

/**
 * Returns the normalized metadata property `aria.naming`.
 *
 * - Returns `allowed` if this element allows naming.
 * - Returns `prohibited` if this element does not allow naming.
 * - If the element doesn't have metadata `allowed` is returned.
 *
 * If the element contains an explicit `role` the role is used to determine
 * whenever the element allows naming or not. Otherwise it uses metadata to
 * determine.
 *
 * @public
 * @param element - Element to get `aria.naming` from.
 */
export declare function ariaNaming(element: HtmlElement): "allowed" | "prohibited";

/**
 * DOM Attribute.
 *
 * Represents a HTML attribute. Can contain either a fixed static value or a
 * placeholder for dynamic values (e.g. interpolated).
 *
 * @public
 */
export declare class Attribute {
    /** Attribute name */
    readonly key: string;
    readonly value: string | DynamicValue | null;
    readonly keyLocation: Location_2;
    readonly valueLocation: Location_2 | null;
    readonly originalAttribute?: string;
    /**
     * @param key - Attribute name.
     * @param value - Attribute value. Set to `null` for boolean attributes.
     * @param keyLocation - Source location of attribute name.
     * @param valueLocation - Source location of attribute value.
     * @param originalAttribute - If this attribute was dynamically added via a
     * transformation (e.g. vuejs `:id` generating the `id` attribute) this
     * parameter should be set to the attribute name of the source attribute (`:id`).
     */
    constructor(key: string, value: string | DynamicValue | null, keyLocation: Location_2, valueLocation: Location_2 | null, originalAttribute?: string);
    /**
     * Flag set to true if the attribute value is static.
     */
    get isStatic(): boolean;
    /**
     * Flag set to true if the attribute value is dynamic.
     */
    get isDynamic(): boolean;
    /**
     * Test attribute value.
     *
     * @param pattern - Pattern to match value against. Can be a RegExp, literal
     * string or an array of strings (returns true if any value matches the
     * array).
     * @param dynamicMatches - If true `DynamicValue` will always match, if false
     * it never matches.
     * @returns `true` if attribute value matches pattern.
     */
    valueMatches(pattern: RegExp | string | string[], dynamicMatches?: boolean): boolean;
}

/**
 * Raw attribute data.
 *
 * @public
 */
export declare interface AttributeData {
    /** Attribute name */
    key: string;
    /** Attribute value */
    value: string | DynamicValue | null;
    /** Quotation mark (if present) */
    quote: '"' | "'" | null;
    /** Original attribute name (when a dynamic attribute is used), e.g
     * "ng-attr-foo" or "v-bind:foo" */
    originalAttribute?: string;
}

/**
 * Event emitted when attributes are encountered.
 *
 * @public
 */
export declare interface AttributeEvent extends Event_2 {
    /** Location of the full attribute (key, quotes and value) */
    location: Location_2;
    /** Attribute name. */
    key: string;
    /** Attribute value. */
    value: string | DynamicValue | null;
    /** Quotemark used. */
    quote: '"' | "'" | null;
    /** Set to original attribute when a transformer dynamically added this
     * attribute. */
    originalAttribute?: string;
    /** HTML element this attribute belongs to. */
    target: HtmlElement;
    /** Location of the attribute key */
    keyLocation: Location_2;
    /** Location of the attribute value */
    valueLocation: Location_2 | null;
    /** Attribute metadata if present */
    meta: MetaAttribute | null;
}

/* Excluded from this release type: AttrNameToken */

/* Excluded from this release type: AttrValueToken */

/**
 * @public
 */
export declare interface AvailableFormatters {
    checkstyle: Formatter;
    codeframe: Formatter;
    json: Formatter;
    stylish: Formatter;
    text: Formatter;
}

/* Excluded from this release type: BaseToken */

/**
 * @public
 */
export declare type CategoryOrTag = string;

/**
 * Create a new resolver for NodeJS packages using `require(..)`.
 *
 * If the module name contains `<rootDir>` (e.g. `<rootDir/foo`) it will be
 * expanded relative to the root directory either explicitly set by the
 * `rootDir` parameter or determined automatically by the closest `package.json`
 * file (starting at the current working directory).
 *
 * @public
 * @since 8.8.0
 */
export declare function cjsResolver(options?: {
    rootDir?: string;
}): CommonJSResolver;

/**
 * Checks text content of an element.
 *
 * Any text is considered including text from descendant elements. Whitespace is
 * ignored.
 *
 * If any text is dynamic `TextClassification.DYNAMIC_TEXT` is returned.
 *
 * @public
 */
export declare function classifyNodeText(node: HtmlElement, options?: TextClassificationOptions): TextClassification;

/**
 * @public
 */
export declare class CLI {
    private options;
    private config;
    private loader;
    private ignored;
    /**
     * Create new CLI helper.
     *
     * Can be used to create tooling with similar properties to bundled CLI
     * script.
     */
    constructor(options?: CLIOptions);
    /**
     * Returns list of files matching patterns and are not ignored. Filenames will
     * have absolute paths.
     *
     * @public
     */
    expandFiles(patterns: string[], options?: ExpandOptions): Promise<string[]>;
    getFormatter(formatters: string): Promise<(report: Report_2) => string>;
    /**
     * Initialize project with a new configuration.
     *
     * A new `.htmlvalidate.json` file will be placed in the path provided by
     * `cwd`.
     */
    init(cwd: string): Promise<InitResult>;
    /**
     * Clear cache.
     *
     * Previously fetched [[HtmlValidate]] instances must either be fetched again
     * or call [[HtmlValidate.flushConfigCache]].
     */
    clearCache(): Promise<void>;
    /* Excluded from this release type: getLoader */
    /**
     * Get HtmlValidate instance with configuration based on options passed to the
     * constructor.
     *
     * @public
     */
    getValidator(): Promise<HtmlValidate>;
    /* Excluded from this release type: getConfig */
    /**
     * Searches ".htmlvalidateignore" files from filesystem and returns `true` if
     * one of them contains a pattern matching given filename.
     */
    private isIgnored;
    private resolveConfig;
}

/**
 * @public
 */
export declare interface CLIOptions {
    configFile?: string;
    /** Comma-separated list of presets to use */
    preset?: string;
    rules?: string | string[];
}

/* Excluded from this release type: CommentToken */

/**
 * CommonJS resolver.
 *
 * @public
 * @since 8.8.0
 */
export declare type CommonJSResolver = Required<Resolver>;

/**
 * Tests if plugin is compatible with html-validate library. Unless the `silent`
 * option is used a warning is displayed on the console.
 *
 * @public
 * @since v5.0.0
 * @param name - Name of plugin
 * @param declared - What library versions the plugin support (e.g. declared peerDependencies)
 * @returns - `true` if version is compatible
 */
export declare function compatibilityCheck(name: string, declared: string, options?: Partial<CompatibilityOptions>): boolean;

/**
 * Options for {@link compatibilityCheck}.
 *
 * @public
 */
export declare interface CompatibilityOptions {
    /** If `true` nothing no output will be generated on console. Default: `false` */
    silent: boolean;
    /* Excluded from this release type: version */
    /** Use custom logging callback. Default: `console.error` */
    logger(this: void, message: string): void;
}

/**
 * Event emitted when Internet Explorer conditionals `<![if ...]>` are
 * encountered.
 *
 * @public
 */
export declare interface ConditionalEvent extends Event_2 {
    /** Event location. */
    location: Location_2;
    /** Condition including markers. */
    condition: string;
    /** The element containing the conditional, if any. */
    parent: HtmlElement | null;
}

/* Excluded from this release type: ConditionalToken */

/**
 * Configuration holder.
 *
 * Each file being validated will have a unique instance of this class.
 *
 * @public
 */
export declare class Config {
    private config;
    private configurations;
    private resolvers;
    private metaTable;
    private plugins;
    private transformers;
    /**
     * Create a new blank configuration. See also `Config.defaultConfig()`.
     */
    static empty(): Config;
    /**
     * Create configuration from object.
     */
    static fromObject(resolvers: Resolver | Resolver[], options: ConfigData, filename?: string | null): Config | Promise<Config>;
    /* Excluded from this release type: fromFile */
    /* Excluded from this release type: validate */
    /**
     * Load a default configuration object.
     */
    static defaultConfig(): Config;
    /* Excluded from this release type: create */
    private init;
    /* Excluded from this release type: __constructor */
    /**
     * Returns true if this configuration is marked as "root".
     */
    isRootFound(): boolean;
    /**
     * Returns a new configuration as a merge of the two. Entries from the passed
     * object takes priority over this object.
     *
     * @public
     * @param rhs - Configuration to merge with this one.
     */
    merge(resolvers: Resolver[], rhs: Config): Config | Promise<Config>;
    private extendConfig;
    private extendConfigAsync;
    /* Excluded from this release type: getMetaTable */
    private getElementsFromEntry;
    /* Excluded from this release type: get */
    /* Excluded from this release type: getRules */
    private static getRulesObject;
    /* Excluded from this release type: getPlugins */
    /* Excluded from this release type: getTransformers */
    private loadPlugins;
    private loadConfigurations;
    private extendMeta;
    /**
     * Resolve all configuration and return a [[ResolvedConfig]] instance.
     *
     * A resolved configuration will merge all extended configs and load all
     * plugins and transformers, and normalize the rest of the configuration.
     *
     * @public
     */
    resolve(): ResolvedConfig | Promise<ResolvedConfig>;
    /* Excluded from this release type: resolveData */
}

/**
 * @public
 */
export declare interface ConfigData {
    /**
     * If set to true no new configurations will be searched.
     */
    root?: boolean;
    /**
     * List of configuration presets to extend.
     *
     * The following sources are allowed:
     *
     * - One of the [predefined presets](http://html-validate.org/rules/presets.html).
     * - Node module exporting a preset.
     * - Plugin exporting a named preset.
     * - Local path to a json or js file exporting a preset.
     */
    extends?: string[];
    /**
     * List of sources for element metadata.
     *
     * The following sources are allowed:
     *
     * - "html5" (default) for the builtin metadata.
     * - node module which export metadata
     * - local path to json or js file exporting metadata.
     * - object with inline metadata
     *
     * If elements isn't specified it defaults to `["html5"]`
     */
    elements?: Array<string | Record<string, unknown>>;
    /**
     * List of plugins.
     *
     * Each plugin must be resolvable be require and export the plugin interface.
     */
    plugins?: Array<string | Plugin_2>;
    /**
     * List of source file transformations. A transformer takes a filename and
     * returns Source instances with extracted HTML-templates.
     *
     * Example:
     *
     * ```js
     * "transform": {
     *   "^.*\\.foo$": "my-transform"
     * }
     * ```
     *
     * To run the "my-transform" module on all .foo files.
     */
    transform?: TransformMap;
    rules?: RuleConfig;
}

/* Excluded from this release type: ConfigError */

/**
 * Configuration loader interface.
 *
 * A configuration loader takes a handle (typically a filename) and returns a
 * configuration for it.
 *
 * @public
 */
export declare abstract class ConfigLoader {
    private _globalConfig;
    private _configData;
    protected readonly resolvers: Resolver[];
    /**
     * Create a new ConfigLoader.
     *
     * @param resolvers - Sorted list of resolvers to use (in order).
     * @param configData - Default configuration (which all configurations will inherit from).
     */
    constructor(resolvers: Resolver[], configData?: ConfigData);
    /* Excluded from this release type: setConfigData */
    /**
     * Get the global configuration.
     *
     * @returns A promise resolving to the global configuration.
     */
    protected getGlobalConfig(): Config | Promise<Config>;
    /**
     * Get the global configuration.
     *
     * The synchronous version does not support async resolvers.
     *
     * @returns The global configuration.
     */
    protected getGlobalConfigSync(): Config;
    /**
     * Get configuration for given handle.
     *
     * Handle is typically a filename but it is up to the loader to interpret the
     * handle as something useful.
     *
     * If [[configOverride]] is set it is merged with the final result.
     *
     * @param handle - Unique handle to get configuration for.
     * @param configOverride - Optional configuration to merge final results with.
     */
    abstract getConfigFor(handle: string, configOverride?: ConfigData): ResolvedConfig | Promise<ResolvedConfig>;
    /* Excluded from this release type: getResolvers */
    /**
     * Flush configuration cache.
     *
     * Flushes all cached entries unless a specific handle is given.
     *
     * @param handle - If given only the cache for given handle will be flushed.
     */
    abstract flushCache(handle?: string): void;
    /* Excluded from this release type: _getGlobalConfig */
    /**
     * Default configuration used when no explicit configuration is passed to constructor.
     */
    protected abstract defaultConfig(): Config | Promise<Config>;
    protected empty(): Config;
    /**
     * Load configuration from object.
     */
    protected loadFromObject(options: ConfigData, filename?: string | null): Config | Promise<Config>;
    /**
     * Load configuration from filename.
     */
    protected loadFromFile(filename: string): Config | Promise<Config>;
}

/* Excluded from this release type: configPresets */

/**
 * Configuration ready event.
 *
 * @public
 */
export declare interface ConfigReadyEvent extends Event_2 {
    config: ResolvedConfig;
    rules: Record<string, Rule<unknown, unknown>>;
}

/**
 * @public
 */
declare type CSSStyleDeclaration_2 = Record<string, string>;
export { CSSStyleDeclaration_2 as CSSStyleDeclaration }

/**
 * @public
 */
export declare interface DeferredMessage extends Omit<Message, "selector"> {
    selector: () => string | null;
}

/**
 * Helper function to assist IDE with completion and type-checking.
 *
 * @public
 * @since
 */
export declare function defineConfig(config: ConfigData): ConfigData;

/**
 * Helper function to assist IDE with completion and type-checking.
 *
 * @public
 */
export declare function defineMetadata(metatable: MetaDataTable): MetaDataTable;

/**
 * Helper function to assist IDE with completion and type-checking.
 *
 * @public
 */
export declare function definePlugin(plugin: Plugin_2): Plugin_2;

/**
 * @public
 */
export declare interface DeprecatedElement {
    message?: string;
    documentation?: string;
    source?: string;
}

/**
 * Event emitted when html-validate directives `<!-- [html-validate-...] -->`
 * are encountered.
 *
 * @public
 */
export declare interface DirectiveEvent extends Event_2 {
    /** Event location. */
    location: Location_2;
    /** Action location */
    actionLocation: Location_2;
    /** Options location */
    optionsLocation?: Location_2;
    /** Comment location */
    commentLocation?: Location_2;
    /** Directive action. */
    action: "enable" | "disable" | "disable-block" | "disable-next";
    /** Directive options. */
    data: string;
    /** Directive comment. */
    comment: string;
}

/* Excluded from this release type: DirectiveToken */

/* Excluded from this release type: DoctypeCloseToken */

/**
 * Event emitted when doctypes `<!DOCTYPE ..>` are encountered.
 *
 * @public
 */
export declare interface DoctypeEvent extends Event_2 {
    /** Event location. */
    location: Location_2;
    /** Tag */
    tag: string;
    /** Selected doctype */
    value: string;
    /** Location of doctype value */
    valueLocation: Location_2;
}

/* Excluded from this release type: DoctypeOpenToken */

/* Excluded from this release type: DoctypeValueToken */

/**
 * @public
 */
export declare type DOMInternalID = number;

/**
 * Event emitted after initialization but before tokenization and parsing occurs.
 * Can be used to initialize state in rules.
 *
 * @public
 */
export declare interface DOMLoadEvent extends Event_2 {
    source: Source;
}

/**
 * @public
 */
export declare class DOMNode {
    readonly nodeName: string;
    readonly nodeType: NodeType;
    readonly childNodes: DOMNode[];
    readonly location: Location_2;
    /* Excluded from this release type: unique */
    private cache;
    /**
     * Set of disabled rules for this node.
     *
     * Rules disabled by using directives are added here.
     */
    private disabledRules;
    /**
     * Set of blocked rules for this node.
     *
     * Rules blocked by using directives are added here.
     */
    private blockedRules;
    /* Excluded from this release type: __constructor */
    /* Excluded from this release type: cacheEnable */
    /**
     * Fetch cached value from this DOM node.
     *
     * Cache is not enabled until `cacheEnable()` is called by [[Parser]] (when
     * the element is fully constructed).
     *
     * @returns value or `undefined` if the value doesn't exist.
     */
    cacheGet<K extends keyof DOMNodeCache>(key: K): DOMNodeCache[K] | undefined;
    cacheGet(key: string | number | symbol): any | undefined;
    /**
     * Store a value in cache.
     *
     * @returns the value itself is returned.
     */
    cacheSet<K extends keyof DOMNodeCache>(key: K, value: DOMNodeCache[K]): DOMNodeCache[K];
    cacheSet<T>(key: string | number | symbol, value: T): T;
    /**
     * Remove a value by key from cache.
     *
     * @returns `true` if the entry existed and has been removed.
     */
    cacheRemove(key: string | number | symbol): boolean;
    /**
     * Check if key exists in cache.
     */
    cacheExists(key: string | number | symbol): boolean;
    /**
     * Get the text (recursive) from all child nodes.
     */
    get textContent(): string;
    append(node: DOMNode): void;
    /* Excluded from this release type: insertBefore */
    isRootElement(): boolean;
    /**
     * Tests if two nodes are the same (references the same object).
     *
     * @since v4.11.0
     */
    isSameNode(otherNode: DOMNode): boolean;
    /**
     * Returns a DOMNode representing the first direct child node or `null` if the
     * node has no children.
     */
    get firstChild(): DOMNode;
    /**
     * Returns a DOMNode representing the last direct child node or `null` if the
     * node has no children.
     */
    get lastChild(): DOMNode;
    /* Excluded from this release type: removeChild */
    /* Excluded from this release type: blockRule */
    /* Excluded from this release type: blockRules */
    /* Excluded from this release type: disableRule */
    /* Excluded from this release type: disableRules */
    /**
     * Enable a previously disabled rule for this node.
     */
    enableRule(ruleId: string): void;
    /**
     * Enables multiple rules.
     */
    enableRules(rules: string[]): void;
    /* Excluded from this release type: ruleEnabled */
    /* Excluded from this release type: ruleBlockers */
    generateSelector(): string | null;
    /* Excluded from this release type: _setParent */
    private _removeChild;
}

/**
 * @public
 */
export declare interface DOMNodeCache {
}

/**
 * Event emitted when DOM tree is fully constructed.
 *
 * @public
 */
export declare interface DOMReadyEvent extends Event_2 {
    /** DOM Tree */
    document: DOMTree;
    source: Source;
}

/**
 * @public
 */
declare class DOMTokenList_2 extends Array<string> {
    readonly value: string;
    private readonly locations;
    constructor(value: string | DynamicValue | null, location: Location_2 | null);
    item(n: number): string | undefined;
    location(n: number): Location_2 | undefined;
    contains(token: string): boolean;
    iterator(): Generator<{
        index: number;
        item: string;
        location: Location_2;
    }>;
}
export { DOMTokenList_2 as DOMTokenList }

/**
 * @public
 */
export declare class DOMTree {
    readonly root: HtmlElement;
    private active;
    private _readyState;
    doctype: string | null;
    /* Excluded from this release type: __constructor */
    /* Excluded from this release type: pushActive */
    /* Excluded from this release type: popActive */
    /* Excluded from this release type: getActive */
    /**
     * Describes the loading state of the document.
     *
     * When `"loading"` it is still not safe to use functions such as
     * `querySelector` or presence of attributes, child nodes, etc.
     */
    get readyState(): "loading" | "complete";
    /* Excluded from this release type: resolveMeta */
    getElementsByTagName(tagName: string): HtmlElement[];
    /**
     * @deprecated use utility function `walk.depthFirst(..)` instead (since 8.21.0).
     */
    visitDepthFirst(callback: (node: HtmlElement) => void): void;
    /**
     * @deprecated use `querySelector(..)` instead (since 8.21.0)
     */
    find(callback: (node: HtmlElement) => boolean): HtmlElement | null;
    querySelector(selector: string): HtmlElement | null;
    querySelectorAll(selector: string): HtmlElement[];
}

/**
 * @public
 */
export declare class DynamicValue {
    readonly expr: string;
    constructor(expr: string);
    toString(): string;
}

/**
 * Event emitted when an element is fully constructed (including its children).
 *
 * @public
 */
export declare interface ElementReadyEvent extends Event_2 {
    /** Event location. */
    location: Location_2;
    /** HTML element */
    target: HtmlElement;
}

/* Excluded from this release type: EOFToken */

/**
 * @public
 */
export declare interface ErrorDescriptor<ContextType> {
    node: DOMNode | null;
    message: string;
    location?: Location_2 | null;
    context?: ContextType;
}

/**
 * ESM resolver.
 *
 * @public
 * @since 9.0.0
 */
export declare type ESMResolver = Required<Resolver>;

/**
 * Create a new resolver for NodeJS packages using `import(..)`.
 *
 * If the module name contains `<rootDir>` (e.g. `<rootDir/foo`) it will be
 * expanded relative to the root directory either explicitly set by the
 * `rootDir` parameter or determined automatically by the closest `package.json`
 * file (starting at the current working directory).
 *
 * @public
 * @since 9.0.0
 */
export declare function esmResolver(options?: {
    rootDir?: string;
}): ESMResolver;

/**
 * @public
 */
declare interface Event_2 {
    /** Event location. */
    location: Location_2 | null;
}
export { Event_2 as Event }

/**
 * @public
 */
export declare type EventCallback = (event: string, data: any) => void;

/* Excluded from this release type: EventDump */

/**
 * @public
 */
export declare class EventHandler {
    private listeners;
    constructor();
    /**
     * Add an event listener.
     *
     * @param event - Event names (comma separated) or '*' for any event.
     * @param callback - Called any time even triggers.
     * @returns Unregistration function.
     */
    on(event: string, callback: EventCallback): () => void;
    /**
     * Add a onetime event listener. The listener will automatically be removed
     * after being triggered once.
     *
     * @param event - Event names (comma separated) or '*' for any event.
     * @param callback - Called any time even triggers.
     * @returns Unregistration function.
     */
    once(event: string, callback: EventCallback): () => void;
    /**
     * Trigger event causing all listeners to be called.
     *
     * @param event - Event name.
     * @param data - Event data.
     */
    trigger(event: string, data: any): void;
    private getCallbacks;
}

/**
 * @public
 */
export declare interface ExpandOptions {
    /**
     * Working directory. Defaults to `process.cwd()`.
     */
    cwd?: string;
    /**
     * List of extensions to search for when expanding directories. Extensions
     * should be passed without leading dot, e.g. "html" instead of ".html".
     */
    extensions?: string[];
}

/**
 * Loads configuration by traversing filesystem.
 *
 * Configuration is read from three sources and in the following order:
 *
 * 1. Global configuration passed to constructor.
 * 2. Configuration files found when traversing the directory structure.
 * 3. Override passed to this function.
 *
 * The following configuration filenames are searched:
 *
 * - `.htmlvalidate.json`
 * - `.htmlvalidate.js`
 * - `.htmlvalidate.cjs`
 * - `.htmlvalidate.mjs`
 *
 * Global configuration is used when no configuration file is found. The
 * result is always merged with override if present.
 *
 * The `root` property set to `true` affects the configuration as following:
 *
 * 1. If set in override the override is returned as-is.
 * 2. If set in the global config the override is merged into global and
 * returned. No configuration files are searched.
 * 3. Setting `root` in configuration file only stops directory traversal.
 *
 * @public
 */
export declare class FileSystemConfigLoader extends ConfigLoader {
    protected cache: Map<string, Config | null>;
    private fs;
    /**
     * Create a filesystem configuration loader with default resolvers.
     *
     * @param fs - `fs` implementation,
     * @param config - Global configuration.
     * @param configFactory - Optional configuration factory.
     */
    constructor(config?: ConfigData, options?: Partial<FileSystemConfigLoaderOptions>);
    /**
     * Create a filesystem configuration loader with custom resolvers.
     *
     * @param fs - `fs` implementation,
     * @param resolvers - Resolvers to use.
     * @param config - Global configuration.
     * @param configFactory - Optional configuration factory.
     */
    constructor(resolvers: Resolver[], config?: ConfigData, options?: Partial<FileSystemConfigLoaderOptions>);
    /**
     * Get configuration for given filename.
     *
     * @param filename - Filename to get configuration for.
     * @param configOverride - Configuration to merge final result with.
     */
    getConfigFor(filename: string, configOverride?: ConfigData): ResolvedConfig | Promise<ResolvedConfig>;
    /**
     * Flush configuration cache.
     *
     * @param filename - If given only the cache for that file is flushed.
     */
    flushCache(filename?: string): void;
    /**
     * Load raw configuration from directory traversal.
     *
     * This configuration is not merged with global configuration and may return
     * `null` if no configuration files are found.
     */
    fromFilename(filename: string): Config | Promise<Config | null> | null;
    /* Excluded from this release type: fromFilenameAsync */
    private _merge;
    private _resolveSync1;
    private _resolveSync2;
    private _resolveAsync;
    /* Excluded from this release type: _getInternalCache */
    protected defaultConfig(): Config | Promise<Config>;
}

/**
 * Options for [[FileSystemConfigLoader]].
 *
 * @public
 */
export declare interface FileSystemConfigLoaderOptions {
    /** An implementation of `fs` as needed by [[FileSystemConfigLoader]] */
    fs: FSLike;
}

/**
 * @public
 */
export declare interface FormAssociated {
    /** This element can be disabled using the `disabled` attribute */
    disablable: boolean;
    /** Listed elements have a name attribute and is listed in the form and fieldset elements property. */
    listed: boolean;
}

/**
 * @public
 */
export declare type Formatter = (results: Result[]) => string;

/**
 * Get formatter function by name.
 *
 * @public
 * @param name - Name of formatter.
 * @returns Formatter function or null if it doesn't exist.
 */
export declare function formatterFactory(name: keyof AvailableFormatters): Formatter;

/**
 * @public
 */
export declare function formatterFactory(name: string): Formatter | null;

/**
 * @public
 */
export declare interface FSLike {
    existsSync(path: string): boolean;
}

/**
 * @public
 */
export declare class HtmlElement extends DOMNode {
    readonly tagName: string;
    readonly voidElement: boolean;
    readonly depth: number;
    closed: NodeClosed;
    protected readonly attr: Record<string, Attribute[]>;
    private metaElement;
    private annotation;
    private _parent;
    /* Excluded from this release type: _adapter */
    private constructor();
    /**
     * Manually create a new element. This is primary useful for test-cases. While
     * the API is public it is not meant for general consumption and is not
     * guaranteed to be stable across versions.
     *
     * Use at your own risk. Prefer to use [[Parser]] to parse a string of markup
     * instead.
     *
     * @public
     * @since 8.22.0
     * @param tagName - Element tagname.
     * @param location - Element location.
     * @param details - Additional element details.
     */
    static createElement(tagName: string, location: Location_2, details?: {
        closed?: NodeClosed;
        meta?: MetaElement | null;
        parent?: HtmlElement;
    }): HtmlElement;
    /* Excluded from this release type: rootNode */
    /* Excluded from this release type: fromTokens */
    /**
     * Returns annotated name if set or defaults to `<tagName>`.
     *
     * E.g. `my-annotation` or `<div>`.
     */
    get annotatedName(): string;
    /**
     * Get list of IDs referenced by `aria-labelledby`.
     *
     * If the attribute is unset or empty this getter returns null.
     * If the attribute is dynamic the original {@link DynamicValue} is returned.
     *
     * @public
     */
    get ariaLabelledby(): string[] | DynamicValue | null;
    /**
     * Similar to childNodes but only elements.
     */
    get childElements(): HtmlElement[];
    /**
     * Find the first ancestor matching a selector.
     *
     * Implementation of DOM specification of Element.closest(selectors).
     */
    closest(selectors: string): HtmlElement | null;
    /**
     * Generate a DOM selector for this element. The returned selector will be
     * unique inside the current document.
     */
    generateSelector(): string | null;
    /**
     * Tests if this element has given tagname.
     *
     * If passing "*" this test will pass if any tagname is set.
     */
    is(tagName: string): boolean;
    /**
     * Load new element metadata onto this element.
     *
     * Do note that semantics such as `void` cannot be changed (as the element has
     * already been created). In addition the element will still "be" the same
     * element, i.e. even if loading meta for a `<p>` tag upon a `<div>` tag it
     * will still be a `<div>` as far as the rest of the validator is concerned.
     *
     * In fact only certain properties will be copied onto the element:
     *
     * - content categories (flow, phrasing, etc)
     * - required attributes
     * - attribute allowed values
     * - permitted/required elements
     *
     * Properties *not* loaded:
     *
     * - inherit
     * - deprecated
     * - foreign
     * - void
     * - implicitClosed
     * - scriptSupporting
     * - deprecatedAttributes
     *
     * Changes to element metadata will only be visible after `element:ready` (and
     * the subsequent `dom:ready` event).
     */
    loadMeta(meta: MetaElement): void;
    /**
     * Match this element against given selectors. Returns true if any selector
     * matches.
     *
     * Implementation of DOM specification of Element.matches(selectors).
     */
    matches(selectorList: string): boolean;
    get meta(): MetaElement | null;
    get parent(): HtmlElement | null;
    /**
     * Get current role for this element (explicit with `role` attribute or mapped
     * with implicit role).
     *
     * @since 8.9.1
     */
    get role(): string | DynamicValue | null;
    /**
     * Set annotation for this element.
     */
    setAnnotation(text: string): void;
    /**
     * Set attribute. Stores all attributes set even with the same name.
     *
     * @param key - Attribute name
     * @param value - Attribute value. Use `null` if no value is present.
     * @param keyLocation - Location of the attribute name.
     * @param valueLocation - Location of the attribute value (excluding quotation)
     * @param originalAttribute - If attribute is an alias for another attribute
     * (dynamic attributes) set this to the original attribute name.
     */
    setAttribute(key: string, value: string | DynamicValue | null, keyLocation: Location_2, valueLocation: Location_2 | null, originalAttribute?: string): void;
    /**
     * Get parsed tabindex for this element.
     *
     * - If `tabindex` attribute is not present `null` is returned.
     * - If attribute value is omitted or the empty string `null` is returned.
     * - If attribute value cannot be parsed `null` is returned.
     * - If attribute value is dynamic `0` is returned.
     * - Otherwise the parsed value is returned.
     *
     * This property does *NOT* take into account if the element have a default
     * `tabindex` (such as `<input>` have). Instead use the `focusable` metadata
     * property to determine this.
     *
     * @public
     * @since 8.16.0
     */
    get tabIndex(): number | null;
    /**
     * Get a list of all attributes on this node.
     */
    get attributes(): Attribute[];
    hasAttribute(key: string): boolean;
    /**
     * Get attribute.
     *
     * By default only the first attribute is returned but if the code needs to
     * handle duplicate attributes the `all` parameter can be set to get all
     * attributes with given key.
     *
     * This usually only happens when code contains duplicate attributes (which
     * `no-dup-attr` will complain about) or when a static attribute is combined
     * with a dynamic, consider:
     *
     * <p class="foo" dynamic-class="bar">
     *
     * @param key - Attribute name
     * @param all - Return single or all attributes.
     */
    getAttribute(key: string): Attribute | null;
    getAttribute(key: string, all: true): Attribute[];
    /**
     * Get attribute value.
     *
     * Returns the attribute value if present.
     *
     * - Missing attributes return `null`.
     * - Boolean attributes return `null`.
     * - `DynamicValue` returns attribute expression.
     *
     * @param key - Attribute name
     * @returns Attribute value or null.
     */
    getAttributeValue(key: string): string | null;
    /**
     * Add text as a child node to this element.
     *
     * @param text - Text to add.
     * @param location - Source code location of this text.
     */
    appendText(text: string | DynamicValue, location: Location_2): void;
    /**
     * Return a list of all known classes on the element. Dynamic values are
     * ignored.
     */
    get classList(): DOMTokenList_2;
    /**
     * Get element ID if present.
     */
    get id(): string | null;
    get style(): CSSStyleDeclaration_2;
    /**
     * Returns the first child element or null if there are no child elements.
     */
    get firstElementChild(): HtmlElement | null;
    /**
     * Returns the last child element or null if there are no child elements.
     */
    get lastElementChild(): HtmlElement | null;
    get siblings(): HtmlElement[];
    get previousSibling(): HtmlElement | null;
    get nextSibling(): HtmlElement | null;
    getElementsByTagName(tagName: string): HtmlElement[];
    querySelector(selector: string): HtmlElement | null;
    querySelectorAll(selector: string): HtmlElement[];
    private querySelectorImpl;
    /* Excluded from this release type: someChildren */
    /* Excluded from this release type: everyChildren */
    /* Excluded from this release type: find */
    /* Excluded from this release type: _setParent */
}

/**
 * HTML5 interface for HTMLElement. Contains all the needed methods the
 * HTML-Validate metadata requires to determine if usage is valid.
 *
 * While not officially supported, changes to this interface should be verified
 * against browsers and/or jsdom, i.e. it should be possible to pass in either
 * implementation and the element metadata should still work.
 *
 * @public
 * @since 8.2.0
 */
export declare interface HtmlElementLike {
    closest(selectors: string): HtmlElementLike | null | undefined;
    getAttribute(name: string): string | DynamicValue | null | undefined;
    hasAttribute(name: string): boolean;
}

/**
 * Primary API for using HTML-validate.
 *
 * Provides high-level abstractions for common operations.
 *
 * @public
 */
export declare class HtmlValidate {
    protected configLoader: ConfigLoader;
    /**
     * Create a new validator.
     *
     * @public
     * @param configLoader - Use a custom configuration loader.
     * @param config - If set it provides the global default configuration. By
     * default `Config.defaultConfig()` is used.
     */
    constructor(config?: ConfigData);
    constructor(configLoader: ConfigLoader);
    /**
     * Parse and validate HTML from string.
     *
     * @public
     * @param str - Text to parse.
     * @param filename - If set configuration is loaded for given filename.
     * @param hooks - Optional hooks (see [[Source]]) for definition.
     * @returns Report output.
     */
    validateString(str: string): Promise<Report_2>;
    validateString(str: string, filename: string): Promise<Report_2>;
    validateString(str: string, hooks: SourceHooks): Promise<Report_2>;
    validateString(str: string, options: ConfigData): Promise<Report_2>;
    validateString(str: string, filename: string, hooks: SourceHooks): Promise<Report_2>;
    validateString(str: string, filename: string, options: ConfigData): Promise<Report_2>;
    validateString(str: string, filename: string, options: ConfigData, hooks: SourceHooks): Promise<Report_2>;
    /**
     * Parse and validate HTML from string.
     *
     * @public
     * @param str - Text to parse.
     * @param filename - If set configuration is loaded for given filename.
     * @param hooks - Optional hooks (see [[Source]]) for definition.
     * @returns Report output.
     */
    validateStringSync(str: string): Report_2;
    validateStringSync(str: string, filename: string): Report_2;
    validateStringSync(str: string, hooks: SourceHooks): Report_2;
    validateStringSync(str: string, options: ConfigData): Report_2;
    validateStringSync(str: string, filename: string, hooks: SourceHooks): Report_2;
    validateStringSync(str: string, filename: string, options: ConfigData): Report_2;
    validateStringSync(str: string, filename: string, options: ConfigData, hooks: SourceHooks): Report_2;
    /**
     * Parse and validate HTML from [[Source]].
     *
     * @public
     * @param input - Source to parse.
     * @returns Report output.
     */
    validateSource(input: Source, configOverride?: ConfigData): Promise<Report_2>;
    /**
     * Parse and validate HTML from [[Source]].
     *
     * @public
     * @param input - Source to parse.
     * @returns Report output.
     */
    validateSourceSync(input: Source, configOverride?: ConfigData): Report_2;
    /**
     * Parse and validate HTML from file.
     *
     * @public
     * @param filename - Filename to read and parse.
     * @returns Report output.
     */
    validateFile(filename: string, fs?: TransformFS): Promise<Report_2>;
    /**
     * Parse and validate HTML from file.
     *
     * @public
     * @param filename - Filename to read and parse.
     * @returns Report output.
     */
    validateFileSync(filename: string, fs?: TransformFS): Report_2;
    /**
     * Parse and validate HTML from multiple files. Result is merged together to a
     * single report.
     *
     * @param filenames - Filenames to read and parse.
     * @returns Report output.
     */
    validateMultipleFiles(filenames: string[], fs?: TransformFS): Promise<Report_2>;
    /**
     * Parse and validate HTML from multiple files. Result is merged together to a
     * single report.
     *
     * @param filenames - Filenames to read and parse.
     * @returns Report output.
     */
    validateMultipleFilesSync(filenames: string[], fs?: TransformFS): Report_2;
    /**
     * Returns true if the given filename can be validated.
     *
     * A file is considered to be validatable if the extension is `.html` or if a
     * transformer matches the filename.
     *
     * This is mostly useful for tooling to determine whenever to validate the
     * file or not. CLI tools will run on all the given files anyway.
     */
    canValidate(filename: string): Promise<boolean>;
    /**
     * Returns true if the given filename can be validated.
     *
     * A file is considered to be validatable if the extension is `.html` or if a
     * transformer matches the filename.
     *
     * This is mostly useful for tooling to determine whenever to validate the
     * file or not. CLI tools will run on all the given files anyway.
     */
    canValidateSync(filename: string): boolean;
    /* Excluded from this release type: dumpTokens */
    /* Excluded from this release type: dumpEvents */
    /* Excluded from this release type: dumpTree */
    /* Excluded from this release type: dumpSource */
    /**
     * Get effective configuration schema.
     */
    getConfigurationSchema(): Promise<SchemaObject>;
    /**
     * Get effective metadata element schema.
     *
     * If a filename is given the configured plugins can extend the
     * schema. Filename must not be an existing file or a filetype normally
     * handled by html-validate but the path will be used when resolving
     * configuration. As a rule-of-thumb, set it to the elements json file.
     */
    getElementsSchema(filename?: string): Promise<SchemaObject>;
    /**
     * Get effective metadata element schema.
     *
     * If a filename is given the configured plugins can extend the
     * schema. Filename must not be an existing file or a filetype normally
     * handled by html-validate but the path will be used when resolving
     * configuration. As a rule-of-thumb, set it to the elements json file.
     */
    getElementsSchemaSync(filename?: string): SchemaObject;
    /**
     * Get contextual documentation for the given rule. Configuration will be
     * resolved for given filename.
     *
     * @example
     *
     * ```js
     * const report = await htmlvalidate.validateFile("my-file.html");
     * for (const result of report.results){
     *   for (const message of result.messages){
     *     const documentation = await htmlvalidate.getContextualDocumentation(message, result.filePath);
     *     // do something with documentation
     *   }
     * }
     * ```
     *
     * @public
     * @since 8.0.0
     * @param message - Message reported during validation
     * @param filename - Filename used to resolve configuration.
     * @returns Contextual documentation or `null` if the rule does not exist.
     */
    getContextualDocumentation(message: Pick<Message, "ruleId" | "context">, filename?: string): Promise<RuleDocumentation | null>;
    /**
     * Get contextual documentation for the given rule using provided
     * configuration.
     *
     * @example
     *
     * ```js
     * const report = await htmlvalidate.validateFile("my-file.html");
     * for (const result of report.results){
     *   for (const message of result.messages){
     *     const documentation = await htmlvalidate.getRuleDocumentation(message, result.filePath);
     *     // do something with documentation
     *   }
     * }
     * ```
     *
     * @public
     * @since 8.0.0
     * @param message - Message reported during validation
     * @param config - Configuration to use.
     * @returns Contextual documentation or `null` if the rule does not exist.
     */
    getContextualDocumentation(message: Pick<Message, "ruleId" | "context">, config: ResolvedConfig | Promise<ResolvedConfig>): Promise<RuleDocumentation | null>;
    /**
     * Get contextual documentation for the given rule. Configuration will be
     * resolved for given filename.
     *
     * @example
     *
     * ```js
     * const report = htmlvalidate.validateFileSync("my-file.html");
     * for (const result of report.results){
     *   for (const message of result.messages){
     *     const documentation = htmlvalidate.getRuleDocumentationSync(message, result.filePath);
     *     // do something with documentation
     *   }
     * }
     * ```
     *
     * @public
     * @since 8.0.0
     * @param message - Message reported during validation
     * @param filename - Filename used to resolve configuration.
     * @returns Contextual documentation or `null` if the rule does not exist.
     */
    getContextualDocumentationSync(message: Pick<Message, "ruleId" | "context">, filename?: string): RuleDocumentation | null;
    /**
     * Get contextual documentation for the given rule using provided
     * configuration.
     *
     * @example
     *
     * ```js
     * const report = htmlvalidate.validateFileSync("my-file.html");
     * for (const result of report.results){
     *   for (const message of result.messages){
     *     const documentation = htmlvalidate.getRuleDocumentationSync(message, result.filePath);
     *     // do something with documentation
     *   }
     * }
     * ```
     *
     * @public
     * @since 8.0.0
     * @param message - Message reported during validation
     * @param config - Configuration to use.
     * @returns Contextual documentation or `null` if the rule does not exist.
     */
    getContextualDocumentationSync(message: Pick<Message, "ruleId" | "context">, config: ResolvedConfig): RuleDocumentation | null;
    /**
     * Get contextual documentation for the given rule.
     *
     * Typical usage:
     *
     * ```js
     * const report = await htmlvalidate.validateFile("my-file.html");
     * for (const result of report.results){
     *   const config = await htmlvalidate.getConfigFor(result.filePath);
     *   for (const message of result.messages){
     *     const documentation = await htmlvalidate.getRuleDocumentation(message.ruleId, config, message.context);
     *     // do something with documentation
     *   }
     * }
     * ```
     *
     * @public
     * @deprecated Deprecated since 8.0.0, use [[getContextualDocumentation]] instead.
     * @param ruleId - Rule to get documentation for.
     * @param config - If set it provides more accurate description by using the
     * correct configuration for the file.
     * @param context - If set to `Message.context` some rules can provide
     * contextual details and suggestions.
     */
    getRuleDocumentation(ruleId: string, config?: ResolvedConfig | Promise<ResolvedConfig> | null, context?: unknown | null): Promise<RuleDocumentation | null>;
    /**
     * Get contextual documentation for the given rule.
     *
     * Typical usage:
     *
     * ```js
     * const report = htmlvalidate.validateFileSync("my-file.html");
     * for (const result of report.results){
     *   const config = htmlvalidate.getConfigForSync(result.filePath);
     *   for (const message of result.messages){
     *     const documentation = htmlvalidate.getRuleDocumentationSync(message.ruleId, config, message.context);
     *     // do something with documentation
     *   }
     * }
     * ```
     *
     * @public
     * @deprecated Deprecated since 8.0.0, use [[getContextualDocumentationSync]] instead.
     * @param ruleId - Rule to get documentation for.
     * @param config - If set it provides more accurate description by using the
     * correct configuration for the file.
     * @param context - If set to `Message.context` some rules can provide
     * contextual details and suggestions.
     */
    getRuleDocumentationSync(ruleId: string, config?: ResolvedConfig | null, context?: unknown | null): RuleDocumentation | null;
    /* Excluded from this release type: getParserFor */
    /**
     * Get configuration for given filename.
     *
     * See [[FileSystemConfigLoader]] for details.
     *
     * @public
     * @param filename - Filename to get configuration for.
     * @param configOverride - Configuration to apply last.
     */
    getConfigFor(filename: string, configOverride?: ConfigData): Promise<ResolvedConfig>;
    /**
     * Get configuration for given filename.
     *
     * See [[FileSystemConfigLoader]] for details.
     *
     * @public
     * @param filename - Filename to get configuration for.
     * @param configOverride - Configuration to apply last.
     */
    getConfigForSync(filename: string, configOverride?: ConfigData): ResolvedConfig;
    /**
     * Get current configuration loader.
     *
     * @public
     * @since %version%
     * @returns Current configuration loader.
     */
    getConfigLoader(): ConfigLoader;
    /**
     * Set configuration loader.
     *
     * @public
     * @since %version%
     * @param loader - New configuration loader to use.
     */
    setConfigLoader(loader: ConfigLoader): void;
    /**
     * Flush configuration cache. Clears full cache unless a filename is given.
     *
     * See [[FileSystemConfigLoader]] for details.
     *
     * @public
     * @param filename - If set, only flush cache for given filename.
     */
    flushConfigCache(filename?: string): void;
}

/**
 * @public
 */
export declare interface IncludeExcludeOptions {
    include: string[] | null;
    exclude: string[] | null;
}

/**
 * @public
 */
export declare interface InitResult {
    filename: string;
}

/**
 * Returns `true` if the error is a `UserError`, i.e. it is an error thrown that
 * is caused by something the end user caused such as a misconfiguration.
 *
 * @public
 */
export declare function isUserError(error: unknown): error is UserErrorData;

/* Excluded from this release type: keywordPatternMatcher */

/**
 * @public
 */
export declare interface ListenEventMap {
    "config:ready": ConfigReadyEvent;
    "source:ready": SourceReadyEvent;
    /* Excluded from this release type: token */
    "tag:start": TagStartEvent;
    "tag:end": TagEndEvent;
    "tag:ready": TagReadyEvent;
    "element:ready": ElementReadyEvent;
    "dom:load": DOMLoadEvent;
    "dom:ready": DOMReadyEvent;
    doctype: DoctypeEvent;
    attr: AttributeEvent;
    whitespace: WhitespaceEvent;
    conditional: ConditionalEvent;
    directive: DirectiveEvent;
    /* Excluded from this release type: "rule:error" */
    /* Excluded from this release type: "parse:begin" */
    /* Excluded from this release type: "parse:end" */
    "*": Event_2;
}

/* Excluded from this release type: LoadedPlugin */

/**
 * @public
 */
declare interface Location_2 {
    /**
     * The filemane this location refers to.
     */
    readonly filename: string;
    /**
     * The string offset (number of characters into the string) this location
     * refers to.
     */
    readonly offset: number;
    /**
     * The line number in the file.
     */
    readonly line: number;
    /**
     * The column number in the file. Tabs counts as 1 (not expanded).
     */
    readonly column: number;
    /**
     * The number of characters this location refers to. This includes any
     * whitespace characters such as newlines.
     */
    readonly size: number;
}
export { Location_2 as Location }

/**
 * Reported error message.
 *
 * @public
 */
export declare interface Message {
    /** Rule that triggered this message */
    ruleId: string;
    /** URL to description of error */
    ruleUrl?: string;
    /** Severity of the message */
    severity: number;
    /** Message text */
    message: string;
    /** Offset (number of characters) into the source */
    offset: number;
    /** Line number */
    line: number;
    /** Column number */
    column: number;
    /** From start offset, how many characters is this message relevant for */
    size: number;
    /** DOM selector */
    selector: string | null;
    /**
     * Optional error context used to provide context-aware documentation.
     *
     * This context can be passed to [[HtmlValidate#getRuleDocumentation]].
     */
    context?: any;
}

/**
 * Element ARIA metadata.
 *
 * @public
 * @since 8.11.0
 */
export declare interface MetaAria {
    /**
     * Implicit ARIA role.
     *
     * Can be set either to a string (element unconditionally has given role) or a
     * callback (role depends on the context the element is used in).
     *
     * @since 8.11.0
     */
    implicitRole?: string | MetaImplicitRoleCallback;
    /**
     * If set to `"prohibited"` this element may not specify an accessible name
     * with `aria-label` or `aria-labelledby`. Defaults to `"allowed"` if unset.
     *
     * Note: if the element overrides the `role` (i.e. the `role` attribute is set to
     * something other than the implicit role) naming may or may not be allowed
     * depending on the given role instead.
     *
     * @since 8.11.0
     */
    naming?: "allowed" | "prohibited" | ((node: HtmlElementLike) => "allowed" | "prohibited");
}

/**
 * @public
 */
export declare interface MetaAttribute {
    /**
     * If set it should be a function evaluating to an error message or `null` if
     * the attribute is allowed.
     */
    allowed?: MetaAttributeAllowedCallback;
    /**
     * If true this attribute can only take boolean values: `my-attr`, `my-attr="`
     * or `my-attr="my-attr"`.
     */
    boolean?: boolean;
    /**
     * If set this attribute is considered deprecated, set to `true` or a string
     * with more descriptive message.
     */
    deprecated?: boolean | string;
    /**
     * If set it is an exhaustive list of all possible values (as `string` or
     * `RegExp`) this attribute can have (each token if list is set)
     */
    enum?: Array<string | RegExp>;
    /**
     * If `true` this attribute contains space-separated tokens and each token must
     * be valid by itself.
     */
    list?: boolean;
    /**
     * If `true` this attribute can omit the value.
     */
    omit?: boolean;
    /**
     * If set this attribute is required to be present on the element.
     */
    required?: boolean;
}

/**
 * Callback for the `allowed` property of `MetaAttribute`. It takes a node and
 * should return `null` if there is no errors and a string with an error
 * description if there is an error.
 *
 * @public
 * @param node - The node the attribute belongs to.
 * @param attr - The current attribute value being validated.
 */
export declare type MetaAttributeAllowedCallback = (node: HtmlElementLike, attr: string | DynamicValue | null | undefined) => string | null | undefined;

/**
 * Callback for content category properties of `MetaData`. It takes a node and
 * returns whenever the element belongs to the content group or not.
 *
 * @public
 * @since 8.13.0
 * @param node - The node to determine if it belongs in the content category.
 * @returns `true` if the node belongs to the category.
 */
export declare type MetaCategoryCallback = (node: HtmlElementLike) => boolean;

/**
 * Properties listed here can be copied (loaded) onto another element using
 * [[HtmlElement.loadMeta]].
 *
 * @public
 */
export declare const MetaCopyableProperty: Array<keyof MetaElement>;

/**
 * @public
 */
export declare interface MetaData {
    inherit?: string;
    metadata?: boolean | MetaCategoryCallback;
    flow?: boolean | MetaCategoryCallback;
    sectioning?: boolean | MetaCategoryCallback;
    heading?: boolean | MetaCategoryCallback;
    phrasing?: boolean | MetaCategoryCallback;
    embedded?: boolean | MetaCategoryCallback;
    interactive?: boolean | MetaCategoryCallback;
    deprecated?: boolean | string | DeprecatedElement;
    foreign?: boolean;
    void?: boolean;
    transparent?: boolean | string[];
    implicitClosed?: string[];
    scriptSupporting?: boolean;
    /** Mark element as able to receive focus (without explicit `tabindex`) */
    focusable?: boolean | MetaFocusableCallback;
    form?: boolean;
    /** Mark element as a form-associated element */
    formAssociated?: Partial<FormAssociated>;
    labelable?: boolean | MetaLabelableCallback;
    /**
     * Set to `true` if this element should have no impact on DOM
     * ancestry. Default `false`.
     *
     * I.e., the `<template>` element (where allowed) can contain anything, as it
     * does not directly affect the DOM tree.
     */
    templateRoot?: boolean;
    /** @deprecated use {@link MetaAria.implicitRole} instead */
    implicitRole?: MetaImplicitRoleCallback;
    /** WAI-ARIA attributes */
    aria?: MetaAria;
    deprecatedAttributes?: string[];
    requiredAttributes?: string[];
    attributes?: PermittedAttribute;
    permittedContent?: Permitted;
    permittedDescendants?: Permitted;
    permittedOrder?: PermittedOrder;
    permittedParent?: Permitted;
    requiredAncestors?: RequiredAncestors;
    requiredContent?: RequiredContent;
    textContent?: TextContent | `${TextContent}`;
}

/**
 * Helpers when writing element metadata.
 *
 * @public
 */
export declare interface MetadataHelper {
    /** Returns an error if another attribute is omitted, i.e. it requires another attribute to be present to pass. */
    allowedIfAttributeIsPresent(this: void, ...attr: string[]): MetaAttributeAllowedCallback;
    /** Returns an error if another attribute is present, i.e. it requires another attribute to be omitted to pass. */
    allowedIfAttributeIsAbsent(this: void, ...attr: string[]): MetaAttributeAllowedCallback;
    /** Returns an error if another attribute does not have one of the listed values */
    allowedIfAttributeHasValue(this: void, attr: string, value: string[], options?: {
        defaultValue?: string | null;
    }): MetaAttributeAllowedCallback;
    /**
     * Returns an error if the node doesn't have any of the given elements as parent
     *
     * @since 8.2.0
     **/
    allowedIfParentIsPresent(this: void, ...tags: string[]): MetaAttributeAllowedCallback;
}

/**
 * @public
 */
export declare const metadataHelper: MetadataHelper;

/**
 * @public
 */
export declare type MetaDataTable = Record<string, MetaData>;

/**
 * @public
 */
export declare interface MetaElement extends Omit<MetaData, "deprecatedAttributes" | "requiredAttributes"> {
    tagName: string;
    focusable: boolean | MetaFocusableCallback;
    formAssociated?: FormAssociated;
    /**
     * Set to `true` if this element should have no impact on DOM
     * ancestry. Default `false`.
     *
     * I.e., the `<template>` element (where allowed) can contain anything. as it
     * does not directly affect the DOM tree.
     */
    templateRoot: boolean;
    /** @deprecated Use {@link MetaAria.implicitRole} instead */
    implicitRole: MetaImplicitRoleCallback;
    /** WAI-ARIA attributes */
    aria: NormalizedMetaAria;
    attributes: Record<string, MetaAttribute>;
    textContent?: TextContent;
}

/**
 * Callback for the `focusable` property of `MetaData`. It takes a node and
 * returns whenever the element is focusable or not.
 *
 * @public
 * @since 8.9.0
 * @param node - The node to determine if it is focusable.
 * @returns `true` if the node is focusable.
 */
export declare type MetaFocusableCallback = (node: HtmlElementLike) => boolean;

/**
 * Callback for the `implicitRole` property of `MetaAria`. It takes a node and
 * returns the implicit ARIA role, if any.
 *
 * @public
 * @since 8.4.0
 * @param node - The node to get the role from.
 * @returns Implicit ARIA role or null if there is no implicit role.
 */
export declare type MetaImplicitRoleCallback = (node: HtmlElementLike) => string | null;

/**
 * Callback for the `labelable` properties of `MetaData`. It takes a node and
 * returns whenever the element is labelable or not.
 *
 * @public
 * @since 8.29.0
 * @param node - The node to determine if it is labelable.
 * @returns `true` if the node is labelable.
 */
export declare type MetaLabelableCallback = (node: HtmlElementLike) => boolean;

/**
 * Properties listed here can be used to reverse search elements with the given
 * property enabled. See [[MetaTable.getTagsWithProperty]].
 *
 * @public
 */
export declare type MetaLookupableProperty = "metadata" | "flow" | "sectioning" | "heading" | "phrasing" | "embedded" | "interactive" | "deprecated" | "foreign" | "void" | "transparent" | "scriptSupporting" | "focusable" | "form" | "formAssociated" | "labelable";

/**
 * @public
 */
export declare class MetaTable {
    private readonly elements;
    private schema;
    /* Excluded from this release type: __constructor */
    /* Excluded from this release type: init */
    /**
     * Extend validation schema.
     *
     * @public
     */
    extendValidationSchema(patch: SchemaValidationPatch): void;
    /**
     * Load metadata table from object.
     *
     * @public
     * @param obj - Object with metadata to load
     * @param filename - Optional filename used when presenting validation error
     */
    loadFromObject(obj: unknown, filename?: string | null): void;
    /**
     * Get [[MetaElement]] for the given tag. If no specific metadata is present
     * the global metadata is returned or null if no global is present.
     *
     * @public
     * @returns A shallow copy of metadata.
     */
    getMetaFor(tagName: string): MetaElement | null;
    /**
     * Find all tags which has enabled given property.
     *
     * @public
     */
    getTagsWithProperty(propName: MetaLookupableProperty): string[];
    /**
     * Find tag matching tagName or inheriting from it.
     *
     * @public
     */
    getTagsDerivedFrom(tagName: string): string[];
    private addEntry;
    /**
     * Construct a new AJV schema validator.
     */
    private getSchemaValidator;
    /**
     * @public
     */
    getJSONSchema(): SchemaObject;
    /* Excluded from this release type: entries */
    /**
     * Finds the global element definition and merges each known element with the
     * global, e.g. to assign global attributes.
     */
    private resolveGlobal;
    private mergeElement;
    /* Excluded from this release type: resolve */
}

/**
 * @public
 */
export declare class NestedError extends Error {
    constructor(message: string, nested?: Error);
}

/**
 * @public
 */
export declare enum NodeClosed {
    Open = 0,//            element wasn't closed
    EndTag = 1,//          element closed with end tag <p>...</p>
    VoidOmitted = 2,//     void element with omitted end tag <input>
    VoidSelfClosed = 3,//  self-closed void element <input/>
    ImplicitClosed = 4
}

/**
 * CommonJS resolver.
 *
 * @public
 * @deprecated Deprecated alias for [[CommonJSResolver]].
 * @since 8.0.0
 */
export declare type NodeJSResolver = Required<Resolver>;

/**
 * Create a new resolver for NodeJS packages using `require(..)`.
 *
 * If the module name contains `<rootDir>` (e.g. `<rootDir/foo`) it will be
 * expanded relative to the root directory either explicitly set by the
 * `rootDir` parameter or determined automatically by the closest `package.json`
 * file (starting at the current working directory).
 *
 * @public
 * @deprecated Deprecated alias for [[commonjsResolver]].
 * @since 8.0.0
 */
export declare function nodejsResolver(options?: {
    rootDir?: string;
}): NodeJSResolver;

/**
 * @public
 */
export declare enum NodeType {
    ELEMENT_NODE = 1,
    TEXT_NODE = 3,
    DOCUMENT_NODE = 9
}

/**
 * Element ARIA metadata.
 *
 * @public
 * @since 8.11.0
 */
export declare interface NormalizedMetaAria {
    /**
     *
     * Normalized version of {@link MetaAria.implicitRole}. Always a callback
     * returning the role.
     *
     * @since 8.11.0
     * @returns string with role or null if no corresponding role.
     */
    implicitRole(node: HtmlElementLike): string | null;
    /**
     *
     * Normalized version of {@link MetaAria.naming}. Always a callback
     * returning `"allowed"` or `"prohibited"`.
     *
     * @since 8.11.0
     */
    naming(node: HtmlElementLike): "allowed" | "prohibited";
}

/* Excluded from this release type: ParseBeginEvent */

/* Excluded from this release type: ParseEndEvent */

/**
 * Parse HTML document into a DOM tree.
 *
 * @public
 */
export declare class Parser {
    private readonly event;
    private readonly metaTable;
    private currentNamespace;
    private dom;
    /**
     * Create a new parser instance.
     *
     * @public
     * @param config - Configuration
     */
    constructor(config: ResolvedConfig);
    /**
     * Parse HTML markup.
     *
     * @public
     * @param source - HTML markup.
     * @returns DOM tree representing the HTML markup.
     */
    parseHtml(source: string | Source): HtmlElement;
    /**
     * Detect optional end tag.
     *
     * Some tags have optional end tags (e.g. <ul><li>foo<li>bar</ul> is
     * valid). The parser handles this by checking if the element on top of the
     * stack when is allowed to omit.
     */
    private closeOptional;
    /* Excluded from this release type: consume */
    /* Excluded from this release type: consumeTag */
    /* Excluded from this release type: closeElement */
    private processElement;
    /* Excluded from this release type: discardForeignBody */
    /* Excluded from this release type: consumeAttribute */
    /**
     * Takes attribute key token an returns location.
     */
    private getAttributeKeyLocation;
    /**
     * Take attribute value token and return a new location referring to only the
     * value.
     *
     * foo="bar"    foo='bar'    foo=bar    foo      foo=""
     *      ^^^          ^^^         ^^^    (null)   (null)
     */
    private getAttributeValueLocation;
    /**
     * Take attribute key and value token an returns a new location referring to
     * an aggregate location covering key, quotes if present and value.
     */
    private getAttributeLocation;
    /* Excluded from this release type: consumeDirective */
    /* Excluded from this release type: consumeConditional */
    /* Excluded from this release type: consumeComment */
    /* Excluded from this release type: consumeDoctype */
    /* Excluded from this release type: consumeUntil */
    /* Excluded from this release type: consumeUntilMatchingTag */
    private next;
    /**
     * Listen on events.
     *
     * @public
     * @param event - Event name.
     * @param listener - Event callback.
     * @returns A function to unregister the listener.
     */
    on<K extends keyof ListenEventMap>(event: K, listener: (event: string, data: ListenEventMap[K]) => void): () => void;
    on(event: string, listener: EventCallback): () => void;
    /**
     * Listen on single event. The listener is automatically unregistered once the
     * event has been received.
     *
     * @public
     * @param event - Event name.
     * @param listener - Event callback.
     * @returns A function to unregister the listener.
     */
    once<K extends keyof ListenEventMap>(event: K, listener: (event: string, data: ListenEventMap[K]) => void): () => void;
    once(event: string, listener: EventCallback): () => void;
    /* Excluded from this release type: defer */
    /* Excluded from this release type: trigger */
    /* Excluded from this release type: getEventHandler */
    /**
     * Appends a text node to the current element on the stack.
     */
    private appendText;
    /**
     * Trigger close events for any still open elements.
     */
    private closeTree;
}

/**
 * @public
 */
export declare type Permitted = PermittedEntry[];

/**
 * @public
 */
export declare type PermittedAttribute = Record<string, MetaAttribute | Array<string | RegExp> | null>;

/**
 * @public
 */
export declare type PermittedEntry = CategoryOrTag | PermittedGroup | Array<CategoryOrTag | PermittedGroup>;

/**
 * @public
 */
export declare interface PermittedGroup {
    exclude?: string | string[];
}

/**
 * @public
 */
export declare type PermittedOrder = string[];

/**
 * @public
 */
declare interface Plugin_2 {
    /**
     * Name of the plugin.
     *
     * If specified this is the name used when referring to the plugin. Default is
     * to use the name/path the user used when loading the plugin. To be less
     * confusing for users you should use the same name as your package.
     *
     * The name must be a valid package name according to NPM (basically lowercase
     * characters, must not begin with dot, slash or non-url safe characters).
     *
     * Hint: import and use the name from `package.json`.
     */
    name?: string | null;
    /**
     * Initialization callback.
     *
     * Called once per plugin during initialization.
     */
    init?(): void;
    /**
     * Setup callback.
     *
     * Called once per source after engine is initialized.
     *
     * @param source - The source about to be validated. Readonly.
     * @param eventhandler - Eventhandler from parser. Can be used to listen for
     * parser events.
     */
    setup?(source: Source, eventhandler: EventHandler): void;
    /**
     * Configuration presets.
     *
     * Each key should be the unprefixed name which a configuration later can
     * access using `${plugin}:${key}`, e.g. if a plugin named "my-plugin" exposes
     * a preset named "foobar" it can be accessed using:
     *
     * "extends": ["my-plugin:foobar"]
     */
    configs?: Record<string, ConfigData | null> | null;
    /**
     * List of new rules present.
     */
    rules?: Record<string, RuleConstructor<any, any> | null> | null;
    /**
     * Transformer available in this plugin.
     *
     * Can be given either as a single unnamed transformer or an object with
     * multiple named.
     *
     * Unnamed transformers use the plugin name similar to how a standalone
     * transformer would work:
     *
     * ```
     * "transform": {
     *   "^.*\\.foo$": "my-plugin"
     * }
     * ```
     *
     * For named transformers each key should be the unprefixed name which a
     * configuration later can access using `${plugin}:${key}`, e.g. if a plugin
     * named "my-plugin" exposes a transformer named "foobar" it can be accessed
     * using:
     *
     * ```
     * "transform": {
     *   "^.*\\.foo$": "my-plugin:foobar"
     * }
     * ```
     */
    transformer?: Transformer_2 | Record<string, Transformer_2 | null> | null;
    /**
     * Extend metadata validation schema.
     */
    elementSchema?: SchemaValidationPatch | null;
}
export { Plugin_2 as Plugin }

/**
 * @public
 */
export declare type ProcessAttributeCallback = (this: unknown, attr: AttributeData) => Iterable<AttributeData>;

/**
 * @public
 */
export declare type ProcessElementCallback = (this: ProcessElementContext, node: HtmlElement) => void;

/**
 * @public
 */
export declare interface ProcessElementContext {
    getMetaFor(this: void, tagName: string): MetaElement | null;
}

/**
 * Report object returned by [[HtmlValidate]].
 *
 * @public
 */
declare interface Report_2 {
    /** `true` if validation was successful */
    valid: boolean;
    /** Detailed results per validated source */
    results: Result[];
    /** Total number of errors across all sources */
    errorCount: number;
    /** Total warnings of errors across all sources */
    warningCount: number;
}
export { Report_2 as Report }

/**
 * @public
 */
export declare class Reporter {
    protected result: Record<string, DeferredMessage[]>;
    constructor();
    /**
     * Merge two or more reports into a single one.
     *
     * @param reports- Reports to merge.
     * @returns A merged report.
     */
    static merge(reports: Report_2[]): Report_2;
    /**
     * Merge two or more reports into a single one.
     *
     * @param reports- Reports to merge.
     * @returns A promise resolved with the merged report.
     */
    static merge(reports: Promise<Report_2[]> | Array<Promise<Report_2>>): Promise<Report_2>;
    add<ContextType, OptionsType>(rule: Rule<ContextType, OptionsType>, message: string, severity: number, node: DOMNode | null, location: Location_2, context: ContextType): void;
    addManual(filename: string, message: DeferredMessage): void;
    save(sources?: Source[]): Report_2;
    protected isValid(): boolean;
}

/**
 * @public
 */
export declare type RequiredAncestors = string[];

/**
 * @public
 */
export declare type RequiredContent = string[];

/**
 * A resolved configuration is a normalized configuration with all extends,
 * plugins etc resolved.
 *
 * @public
 */
export declare class ResolvedConfig {
    private metaTable;
    private plugins;
    private rules;
    private transformers;
    /** The original data this resolved configuration was created from */
    private original;
    /* Excluded from this release type: cache */
    /* Excluded from this release type: __constructor */
    /**
     * Returns the (merged) configuration data used to create this resolved
     * configuration.
     */
    getConfigData(): ConfigData;
    getMetaTable(): MetaTable;
    getPlugins(): Plugin_2[];
    getRules(): Map<string, [Severity, RuleOptions]>;
    /**
     * Returns true if a transformer matches given filename.
     *
     * @public
     */
    canTransform(filename: string): boolean;
    /* Excluded from this release type: findTransformer */
}

/**
 * @public
 */
export declare interface ResolvedConfigData {
    metaTable: MetaTable;
    plugins: Plugin_2[];
    rules: Map<string, [Severity, RuleOptions]>;
    transformers: TransformerEntry[];
}

/**
 * @public
 * @since 8.0.0
 */
export declare interface Resolver {
    /** Name of resolver, mostly for ease of debugging */
    name: string;
    /**
     * Resolve table of element metadata.
     */
    resolveElements?(id: string, options: ResolverOptions): MetaDataTable | Promise<MetaDataTable | null> | null;
    /**
     * Resolve a configuration to extend.
     */
    resolveConfig?(id: string, options: ResolverOptions): ConfigData | Promise<ConfigData | null> | null;
    /**
     * Resolve a plugin.
     */
    resolvePlugin?(id: string, options: ResolverOptions): Plugin_2 | Promise<Plugin_2 | null> | null;
    /**
     * Resolve a transformer.
     */
    resolveTransformer?(id: string, options: ResolverOptions): Transformer_2 | Promise<Transformer_2 | null> | null;
}

/**
 * @public
 * @since 8.0.0
 */
export declare interface ResolverOptions {
    cache: boolean;
}

/**
 * @public
 */
export declare interface Result {
    messages: Message[];
    filePath: string;
    errorCount: number;
    warningCount: number;
    source: string | null;
}

/**
 * @public
 */
export declare abstract class Rule<ContextType = void, OptionsType = void> {
    private reporter;
    private parser;
    private meta;
    private enabled;
    private blockers;
    private severity;
    private event;
    /**
     * Rule name. Defaults to filename without extension but can be overwritten by
     * subclasses.
     */
    name: string;
    /**
     * Rule options.
     */
    readonly options: OptionsType;
    constructor(options: OptionsType);
    getSeverity(): Severity;
    setServerity(severity: Severity): void;
    /* Excluded from this release type: block */
    /* Excluded from this release type: unblock */
    setEnabled(enabled: boolean): void;
    /**
     * Returns `true` if rule is deprecated.
     *
     * Overridden by subclasses.
     */
    get deprecated(): boolean;
    /* Excluded from this release type: isEnabled */
    /* Excluded from this release type: isBlocked */
    /* Excluded from this release type: getBlockers */
    /**
     * Check if keyword is being ignored by the current rule configuration.
     *
     * This method requires the [[RuleOption]] type to include two properties:
     *
     * - include: string[] | null
     * - exclude: string[] | null
     *
     * This methods checks if the given keyword is included by "include" but not
     * excluded by "exclude". If any property is unset it is skipped by the
     * condition. Usually the user would use either one but not both but there is
     * no limitation to use both but the keyword must satisfy both conditions. If
     * either condition fails `true` is returned.
     *
     * For instance, given `{ include: ["foo"] }` the keyword `"foo"` would match
     * but not `"bar"`.
     *
     * Similarly, given `{ exclude: ["foo"] }` the keyword `"bar"` would match but
     * not `"foo"`.
     *
     * @param keyword - Keyword to match against `include` and `exclude` options.
     * @param matcher - Optional function to compare items with.
     * @returns `true` if keyword is not present in `include` or is present in
     * `exclude`.
     */
    isKeywordIgnored(this: {
        options: IncludeExcludeOptions;
    }, keyword: string, matcher?: (list: string[], it: string) => boolean): boolean;
    /**
     * Get [[MetaElement]] for the given tag. If no specific metadata is present
     * the global metadata is returned or null if no global is present.
     *
     * @public
     * @returns A shallow copy of metadata.
     */
    getMetaFor(tagName: string): MetaElement | null;
    /**
     * Find all tags which has enabled given property.
     */
    getTagsWithProperty(propName: MetaLookupableProperty): string[];
    /**
     * Find tag matching tagName or inheriting from it.
     */
    getTagsDerivedFrom(tagName: string): string[];
    /**
     * JSON schema for rule options.
     *
     * Rules should override this to return an object with JSON schema to validate
     * rule options. If `null` or `undefined` is returned no validation is
     * performed.
     */
    static schema(): SchemaObject | null | undefined;
    /**
     * Report a new error.
     *
     * Rule must be enabled both globally and on the specific node for this to
     * have any effect.
     */
    report(error: ErrorDescriptor<ContextType>): void;
    report(node: DOMNode | null, message: string, location?: Location_2 | null): void;
    report(node: DOMNode | null, message: string, location: Location_2 | null | undefined, context: ContextType): void;
    private findLocation;
    /**
     * Listen for events.
     *
     * Adding listeners can be done even if the rule is disabled but for the
     * events to be delivered the rule must be enabled.
     *
     * If the optional filter callback is used it must be a function taking an
     * event of the same type as the listener. The filter is called before the
     * listener and if the filter returns false the event is discarded.
     *
     * @param event - Event name
     * @param filter - Optional filter function. Callback is only called if filter functions return true.
     * @param callback - Callback to handle event.
     * @returns A function to unregister the listener
     */
    on<K extends keyof ListenEventMap>(event: K, callback: (event: ListenEventMap[K]) => void): () => void;
    on<K extends keyof ListenEventMap>(event: K, filter: (event: ListenEventMap[K]) => boolean, callback: (event: ListenEventMap[K]) => void): () => void;
    /* Excluded from this release type: init */
    /* Excluded from this release type: validateOptions */
    /**
     * Rule setup callback.
     *
     * Override this to provide rule setup code.
     */
    abstract setup(): void;
    /**
     * Rule documentation callback.
     *
     * Called when requesting additional documentation for a rule. Some rules
     * provide additional context to provide context-aware suggestions.
     *
     * @public
     * @virtual
     * @param context - Error context given by a reported error.
     * @returns Rule documentation and url with additional details or `null` if no
     * additional documentation is available.
     */
    documentation(context: ContextType): RuleDocumentation | null;
}

/* Excluded from this release type: RuleBlocker */

/**
 * @public
 */
export declare type RuleConfig = Record<string, RuleSeverity | [RuleSeverity] | [RuleSeverity, RuleOptions]>;

/**
 * @public
 */
export declare interface RuleConstructor<T, U> {
    new (options?: any): Rule<T, U>;
    schema(): SchemaObject | null | undefined;
}

/**
 * @public
 */
export declare interface RuleDocumentation {
    description: string;
    url?: string;
}

/* Excluded from this release type: RuleErrorEvent */

/**
 * Returns true if given ruleId is an existing builtin rule. It does not handle
 * rules loaded via plugins.
 *
 * Can be used to create forward/backward compatibility by checking if a rule
 * exists to enable/disable it.
 *
 * @public
 * @param ruleId - Rule id to check
 * @returns `true` if rule exists
 */
export declare function ruleExists(ruleId: string): boolean;

/**
 * @public
 */
export declare type RuleOptions = string | number | Record<string, any>;

/**
 * @public
 */
export declare type RuleSeverity = "off" | "warn" | "error" | 0 | 1 | 2;

export { SchemaObject }

/* Excluded from this release type: SchemaValidationError */

/**
 * @public
 */
export declare interface SchemaValidationPatch {
    properties?: Record<string, unknown>;
    definitions?: Record<string, unknown>;
}

/* Excluded from this release type: ScriptToken */

/**
 * @public
 */
export declare enum Severity {
    DISABLED = 0,
    WARN = 1,
    ERROR = 2
}

/**
 * Calculate a new location by offsetting this location.
 *
 * If the references text with newlines the wrap parameter must be set to
 * properly calculate line and column information. If not given the text is
 * assumed to contain no newlines.
 *
 * @public
 * @param location - Source location
 * @param begin - Start location. Default is 0.
 * @param end - End location. Default is size of location. Negative values are
 * counted from end, e.g. `-2` means `size - 2`.
 * @param wrap - If given, line/column is wrapped for each newline occuring
 * before location end.
 */
export declare function sliceLocation(location: Location_2, begin: number, end?: number, wrap?: string): Location_2;

/**
 * @public
 */
export declare function sliceLocation(location: Location_2 | null | undefined, begin: number, end?: number, wrap?: string): Location_2 | null;

/**
 * Source interface.
 *
 * HTML source with file, line and column context.
 *
 * Optional hooks can be attached. This is usually added by transformers to
 * postprocess.
 *
 * @public
 */
export declare interface Source {
    data: string;
    filename: string;
    /**
     * Line in the original data.
     *
     * Starts at 1 (first line).
     */
    line: number;
    /**
     * Column in the original data.
     *
     * Starts at 1 (first column).
     */
    column: number;
    /**
     * Offset in the original data.
     *
     * Starts at 0 (first character).
     */
    offset: number;
    /**
     * Original data. When a transformer extracts a portion of the original source
     * this must be set to the full original source.
     *
     * Since the transformer might be chained always test if the input source
     * itself has `originalData` set, e.g.:
     *
     * `originalData = input.originalData || input.data`.
     */
    originalData?: string;
    /**
     * Hooks for processing the source as it is being parsed.
     */
    hooks?: SourceHooks;
    /**
     * Internal property to keep track of what transformers has run on this
     * source. Entries are in reverse-order, e.g. the last applied transform is
     * first.
     */
    transformedBy?: string[];
}

/**
 * @public
 */
export declare interface SourceHooks {
    /**
     * Called for every attribute.
     *
     * The original attribute must be yielded as well or no attribute will be
     * added.
     *
     * @returns Attribute data for an attribute to be added to the element.
     */
    processAttribute?: ProcessAttributeCallback | null;
    /**
     * Called for every element after element is created but before any children.
     *
     * May modify the element.
     */
    processElement?: ProcessElementCallback | null;
}

/**
 * Source ready event. Emitted after source has been transformed but before any
 * markup is processed.
 *
 * The source object must not be modified (use a transformer if modifications
 * are required)
 *
 * @public
 */
export declare interface SourceReadyEvent extends Event_2 {
    source: Source;
}

/**
 * The static configuration loader does not do any per-handle lookup. Only the
 * global or per-call configuration is used.
 *
 * In practice this means no configuration is fetched by traversing the
 * filesystem.
 *
 * @public
 */
export declare class StaticConfigLoader extends ConfigLoader {
    /**
     * Create a static configuration loader with default resolvers.
     *
     * @param config - Global configuration
     * @param configFactory - Optional configuration factory
     */
    constructor(config?: ConfigData);
    /**
     * Create a static configuration loader with custom resolvers.
     *
     * @param resolvers - Resolvers to use
     * @param config - Global configuration
     * @param configFactory - Optional configuration factory
     */
    constructor(resolvers: Resolver[], config?: ConfigData);
    /**
     * Set a new configuration for this loader.
     *
     * @public
     * @since 8.20.0
     * @param config - New configuration to use.
     */
    setConfig(config: ConfigData): void;
    getConfigFor(_handle: string, configOverride?: ConfigData): ResolvedConfig | Promise<ResolvedConfig>;
    flushCache(): void;
    protected defaultConfig(): Config | Promise<Config>;
    private _resolveConfig;
}

/**
 * Static resolver.
 *
 * @public
 * @since 8.0.0
 */
export declare interface StaticResolver extends Required<Resolver> {
    addElements(id: string, elements: MetaDataTable): void;
    addConfig(id: string, config: ConfigData): void;
    addPlugin(id: string, plugin: Plugin_2): void;
    addTransformer(id: string, transformer: Transformer_2): void;
}

/**
 * Create a new resolver for static content, i.e. plugins or transformers known
 * at compile time.
 *
 * @public
 * @since 8.0.0
 */
export declare function staticResolver(map?: StaticResolverMap): StaticResolver;

/**
 * Entries for the static resolver.
 *
 * @public
 * @since 8.0.0
 */
export declare interface StaticResolverMap {
    elements?: Record<string, MetaDataTable>;
    configs?: Record<string, ConfigData>;
    plugins?: Record<string, Plugin_2>;
    transformers?: Record<string, Transformer_2>;
}

/* Excluded from this release type: StyleToken */

/* Excluded from this release type: TagCloseToken */

/**
 * Event emitted when end tags `</..>` are encountered.
 *
 * @public
 */
export declare interface TagEndEvent extends Event_2 {
    /** Event location. */
    location: Location_2;
    /** Temporary node for the end tag. Can be null for elements left unclosed
     * when document ends */
    target: HtmlElement | null;
    /** The node being closed. */
    previous: HtmlElement;
}

/* Excluded from this release type: TagOpenToken */

/**
 * Event emitted when a tag is ready (i.e. all the attributes has been
 * parsed). The children of the element will not yet be finished.
 *
 * @public
 */
export declare interface TagReadyEvent extends Event_2 {
    /** Event location. */
    location: Location_2;
    /** The node that is finished parsing. */
    target: HtmlElement;
}

/**
 * Event emitted when starting tags are encountered.
 *
 * @public
 */
export declare interface TagStartEvent extends Event_2 {
    /** Event location. */
    location: Location_2;
    /** The node being started. */
    target: HtmlElement;
}

/* Excluded from this release type: TemplatingToken */

/**
 * @public
 */
export declare enum TextClassification {
    EMPTY_TEXT = 0,
    DYNAMIC_TEXT = 1,
    STATIC_TEXT = 2
}

/**
 * @public
 */
export declare interface TextClassificationOptions {
    /** If `true` only accessible text is considered (default false) */
    accessible?: boolean;
    /** If `true` the `hidden` and `aria-hidden` attribute is ignored on the root
     * (and parents) elements (default false) */
    ignoreHiddenRoot?: boolean;
}

/**
 * @public
 */
export declare enum TextContent {
    NONE = "none",
    DEFAULT = "default",
    REQUIRED = "required",
    ACCESSIBLE = "accessible"
}

/**
 * Represents a text in the HTML document.
 *
 * Text nodes are appended as children of `HtmlElement` and cannot have childen
 * of its own.
 *
 * @public
 */
export declare class TextNode extends DOMNode {
    private readonly text;
    /**
     * @param text - Text to add. When a `DynamicValue` is used the expression is
     * used as "text".
     * @param location - Source code location of this node.
     */
    constructor(text: string | DynamicValue, location: Location_2);
    /**
     * Get the text from node.
     */
    get textContent(): string;
    /**
     * Flag set to true if the attribute value is static.
     */
    get isStatic(): boolean;
    /**
     * Flag set to true if the attribute value is dynamic.
     */
    get isDynamic(): boolean;
}

/* Excluded from this release type: TextToken */

/* Excluded from this release type: Token */

/* Excluded from this release type: TokenDump */

/* Excluded from this release type: TokenEvent */

/* Excluded from this release type: TokenStream */

/* Excluded from this release type: TokenType */

/**
 * @public
 */
export declare interface TransformContext {
    /**
     * Test if an additional chainable transformer is present.
     *
     * Returns true only if there is a transformer configured for the given
     * filename.
     *
     * @param filename - Filename to use to match next transformer.
     */
    hasChain(filename: string): boolean;
    /**
     * Chain transformations.
     *
     * Sometimes multiple transformers must be applied. For instance, a Markdown
     * file with JSX in a code-block.
     *
     * @param source - Source to chain transformations on.
     * @param filename - Filename to use to match next transformer (unrelated to
     * filename set in source)
     */
    chain(source: Source, filename: string): TransformerChainedResult;
}

/**
 * Transform a file or text into one or more plain HTML.
 *
 * @public
 */
declare interface Transformer_2 {
    /**
     * Callback function to transform a source to plain HTML sources.
     *
     * Since 9.0.0 the transformer may return:
     *
     * - A single `Source` object or a `Promise` resolving to one.
     * - An array of `Source` objects or a `Promise` resolving to one.
     * - An array of `Promise` resolving to `Source` objects.
     */
    (this: TransformContext, source: Source): TransformerResult;
    /**
     * API version. Must be specified, it is deprecated to leave it out as it
     * assumes version 0 (deprecated version).
     */
    api?: number;
}
export { Transformer_2 as Transformer }

/**
 * The result of a transformer chain.
 *
 * Similar to the regular `TransformerResult` but flattened for easier usage.
 *
 * @public
 * @since 9.0.0
 */
export declare type TransformerChainedResult = Iterable<Source> | Promise<Iterable<Source>>;

/**
 * Describes a mapping between filename pattern (regex) and the configured
 * transformer.
 *
 * Either an imported via name or directly set as a function.
 *
 * @public
 */
export declare type TransformerEntry = {
    kind: "import";
    pattern: RegExp;
    name: string;
} | {
    kind: "function";
    pattern: RegExp;
    function: Transformer_2;
};

/**
 * The result of a transformer (or its chain).
 *
 * @public
 * @since 9.0.0
 */
export declare type TransformerResult = Source | Iterable<Source | Promise<Source>> | Promise<Source> | Promise<Source | Iterable<Source | Promise<Source>>>;

/**
 * File system API required by transform functions.
 *
 * Compatible with:
 *
 * - `node:fs`
 * - `memfs`
 * - and probably more.
 *
 * @public
 * @since 9.4.0
 */
export declare interface TransformFS {
    /** read file from filesystem */
    readFileSync(this: void, path: string | number, options: {
        encoding: "utf8";
    }): {
        toString(encoding: "utf8"): string;
    } | string;
}

/**
 * @public
 */
export declare type TransformMap = Record<string, string | Transformer_2>;

/**
 * @public
 */
export declare interface TriggerEventMap {
    "config:ready": ConfigReadyEvent;
    "source:ready": SourceReadyEvent;
    /* Excluded from this release type: token */
    "tag:start": TagStartEvent;
    "tag:end": TagEndEvent;
    "tag:ready": TagReadyEvent;
    "element:ready": ElementReadyEvent;
    "dom:load": DOMLoadEvent;
    "dom:ready": DOMReadyEvent;
    doctype: DoctypeEvent;
    attr: AttributeEvent;
    whitespace: WhitespaceEvent;
    conditional: ConditionalEvent;
    directive: DirectiveEvent;
    /* Excluded from this release type: "rule:error" */
    /* Excluded from this release type: "parse:begin" */
    /* Excluded from this release type: "parse:end" */
}

/* Excluded from this release type: UnicodeBOMToken */

/* Excluded from this release type: UserError */

/**
 * Represents a `UserError`, i.e. an error thrown that is caused by something
 * the end user caused such as a misconfiguration.
 *
 * @public
 */
export declare interface UserErrorData extends Error {
    /**
     * Returns a pretty formatted description of the error, if possible.
     */
    prettyFormat(): string | undefined;
}

/**
 * Helper class to validate elements against metadata rules.
 *
 * @public
 */
export declare class Validator {
    /**
     * Test if element is used in a proper context.
     *
     * @param node - Element to test.
     * @param rules - List of rules.
     * @returns `true` if element passes all tests.
     */
    static validatePermitted(node: HtmlElement, rules: Permitted | null): boolean;
    /**
     * Test if an element is used the correct amount of times.
     *
     * For instance, a `<table>` element can only contain a single `<tbody>`
     * child. If multiple `<tbody>` exists this test will fail both nodes.
     * Note that this is called on the parent but will fail the children violating
     * the rule.
     *
     * @param children - Array of children to validate.
     * @param rules - List of rules of the parent element.
     * @returns `true` if the parent element of the children passes the test.
     */
    static validateOccurrences(children: HtmlElement[], rules: Permitted | null, cb: (node: HtmlElement, category: string) => void): boolean;
    /**
     * Validate elements order.
     *
     * Given a parent element with children and metadata containing permitted
     * order it will validate each children and ensure each one exists in the
     * specified order.
     *
     * For instance, for a `<table>` element the `<caption>` element must come
     * before a `<thead>` which must come before `<tbody>`.
     *
     * @param children - Array of children to validate.
     */
    static validateOrder(children: HtmlElement[], rules: PermittedOrder | null, cb: (node: HtmlElement, prev: HtmlElement) => void): boolean;
    /**
     * Validate element ancestors.
     *
     * Check if an element has the required set of elements. At least one of the
     * selectors must match.
     */
    static validateAncestors(node: HtmlElement, rules: RequiredAncestors | null): boolean;
    /**
     * Validate element required content.
     *
     * Check if an element has the required set of elements. At least one of the
     * selectors must match.
     *
     * Returns `[]` when valid or a list of required but missing tagnames or
     * categories.
     */
    static validateRequiredContent(node: HtmlElement, rules: RequiredContent | null): CategoryOrTag[];
    /**
     * Test if an attribute has an allowed value and/or format.
     *
     * @param attr - Attribute to test.
     * @param rules - Element attribute metadta.
     * @returns `true` if attribute passes all tests.
     */
    static validateAttribute(attr: Attribute, rules: Record<string, MetaAttribute | undefined>): boolean;
    private static validateAttributeValue;
    private static validatePermittedRule;
    /**
     * Validate node against a content category.
     *
     * When matching parent nodes against permitted parents use the superset
     * parameter to also match for `@flow`. E.g. if a node expects a `@phrasing`
     * parent it should also allow `@flow` parent since `@phrasing` is a subset of
     * `@flow`.
     *
     * @param node - The node to test against
     * @param category - Name of category with `@` prefix or tag name.
     * @param defaultMatch - The default return value when node categories is not known.
     */
    static validatePermittedCategory(node: HtmlElement, category: string, defaultMatch: boolean): boolean;
}

/** @public */
export declare const version: string;

/**
 * @public
 * @since 8.21.0
 */
export declare interface Walk {
    /**
     * Visits all nodes in the tree starting at node or document in a depth-first
     * (in-order) manner, i.e. any children will be visited before its parent.
     *
     * @public
     * @since 8.21.0
     * @param root - Element or document to visit nodes from.
     * @param callback - Callback executed for each node.
     */
    depthFirst(this: void, root: HtmlElement | DOMTree, callback: (node: HtmlElement) => void): void;
}

/**
 * Helper functions to walk the DOM tree.
 *
 * @public
 * @since 8.21.0
 */
export declare const walk: Walk;

/**
 * Event emitted when whitespace content is parsed.
 *
 * @public
 */
export declare interface WhitespaceEvent extends Event_2 {
    /** Event location. */
    location: Location_2;
    /** Text content. */
    text: string;
}

/* Excluded from this release type: WhitespaceToken */

/**
 * Represents an `Error` created from arbitrary values.
 *
 * @public
 */
export declare class WrappedError extends Error {
    constructor(message: unknown);
}

export { }
